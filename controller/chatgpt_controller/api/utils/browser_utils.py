"""
Browser Control Utilities

Helper functions for controlling browser through WebSocket server,
including page navigation, element interaction, and URL extraction.
"""

import asyncio
import re
import uuid
from datetime import datetime
from typing import Optional, Dict, Any

from ...core.message_types import MessageType
from ...core.events import EventType, subscribe_to_event, unsubscribe_from_event
from ...server.websocket_server import WebSocketServer
from ...utils.logging import RichLogger

logger = RichLogger(__name__)


def extract_conversation_id_from_url(url: str) -> Optional[str]:
    """Extract conversation ID from ChatGPT URL"""
    # Pattern for ChatGPT conversation URLs (frontend)
    pattern1 = r"https://chatgpt\.com/c/([a-f0-9-]+)"
    match = re.search(pattern1, url)
    if match:
        return match.group(1)

    # Pattern for ChatGPT backend API URLs
    pattern2 = r"https://chatgpt\.com/backend-api/conversation/([a-f0-9-]+)"
    match = re.search(pattern2, url)
    if match:
        return match.group(1)

    return None


async def wait_for_element_query_result_async(
    query_id: str,
    timeout: float = 30.0
) -> Optional[Dict[str, Any]]:
    """Event-based async version of wait_for_element_query_result"""
    result_container = {"result": None, "received": False}

    def event_listener(event):
        """Listen for element query result events"""
        try:
            data = event.data
            if (data.get("message_type") == "element_query_result" and
                data.get("data").get('id') == query_id):
                # Extract the result data
                result_container["result"] = {
                    "success": data.get("success", False),
                    "data": data.get("data", {}),
                    "elements": data.get("elements", []),
                    "count": data.get("count", 0),
                    "error": data.get("error"),
                    "execution_time": data.get("execution_time", 0)
                }
                result_container["received"] = True
        except Exception as e:
            logger.debug(f"Error in event listener: {e}")

    # Subscribe to MESSAGE_RECEIVED events
    subscribe_to_event(EventType.MESSAGE_RECEIVED, event_listener)

    try:
        # Wait for the result with timeout
        start_time = asyncio.get_event_loop().time()
        while (asyncio.get_event_loop().time() - start_time) < timeout:
            if result_container["received"]:
                return result_container["result"]
            await asyncio.sleep(0.1)

        # Timeout reached
        return None

    finally:
        # Always unsubscribe to prevent memory leaks
        unsubscribe_from_event(EventType.MESSAGE_RECEIVED, event_listener)


async def wait_for_page_load(wss: WebSocketServer, timeout: float = 5.0, max_retry: int = 5) -> bool:
    """Wait for page to fully load by checking page info"""
    retry_count = 0
    
    async def query_page_info() -> Dict[str, Any]:
        query_id = str(uuid.uuid4())
        command_data = {
            "type": "PAGE_INFO",
            "id": query_id,
            "timestamp": datetime.now().isoformat(),
            "info": ["url", "title", "status"]
        }

        # Send page info query
        success = await wss.send_command(
            MessageType.PAGE_INFO, command_data, message_id=query_id
        )

        if not success:
            logger.warning("Failed to send page info query")
            return None

        # Use event-based async wait
        result = await wait_for_element_query_result_async(query_id, 10.0)
        return result

    while True:
        try:
            result = await query_page_info()
            if result and result.get("success", False):
                data = result.get("data", {})
                if data.get("status") == "complete":
                    logger.debug(f"✅ Page loaded: {data.get('url')}")
                    return True
            logger.debug(f"🔍 Page not ready yet: {result}")
        except Exception as e:
            logger.debug(f"🔍 Page load check failed: {e}")

        retry_count += 1
        if retry_count >= max_retry:
            logger.warning("⚠️ Page load timeout reached")
            return False

        await asyncio.sleep(1.0)


async def get_current_url(websocket_server: WebSocketServer, timeout: float = 10.0) -> Optional[str]:
    """Get current page URL"""
    try:
        # Create page info query for URL
        query_id = str(uuid.uuid4())
        command_data = {
            "type": "PAGE_INFO",
            "id": query_id,
            "timestamp": datetime.now().isoformat(),
            "info": ["url"]
        }

        # Send page info query
        success = await websocket_server.send_command(
            MessageType.PAGE_INFO, command_data, message_id=query_id
        )

        if not success:
            logger.warning("Failed to send page info query for URL")
            return None

        # Wait for response
        result = await wait_for_element_query_result_async(query_id, timeout)
        if result and result.get("success", False):
            page_info = result.get("data", {})
            return page_info.get("url")
        else:
            logger.debug(f"Failed to get page info: {result}")

        return None

    except Exception as e:
        logger.error(f"Error getting current URL: {e}")
        return None


async def click_element_by_xpath(
    websocket_server: WebSocketServer,
    xpath: str,
    timeout: float = 10.0
) -> bool:
    """Click element by XPath"""
    try:
        # Create element query with click action
        query_id = str(uuid.uuid4())
        command_data = {
            "type": "ELEMENT_QUERY",
            "id": query_id,
            "timestamp": datetime.now().isoformat(),
            "selector": {
                "type": "xpath",
                "value": xpath,
                "options": {
                    "waitVisible": True,
                    "timeout": int(timeout * 1000)  # Convert to milliseconds
                }
            },
            "actions": [{
                "type": "click",
                "params": {"delay": 500}
            }]
        }

        # Send element query
        success = await websocket_server.send_command(
            MessageType.ELEMENT_QUERY, command_data, message_id=query_id
        )

        if not success:
            logger.warning(f"Failed to send click command for xpath: {xpath}")
            return False

        # Wait for response
        result = await wait_for_element_query_result_async(query_id, timeout)
        return result is not None and result.get("success", False)

    except Exception as e:
        logger.error(f"Error clicking element by xpath {xpath}: {e}")
        return False


async def input_text_by_xpath(
    websocket_server: WebSocketServer,
    xpath: str,
    text: str,
    timeout: float = 10.0
) -> bool:
    """Input text to element by XPath"""
    try:
        # Create element query with input action
        query_id = str(uuid.uuid4())
        command_data = {
            "type": "ELEMENT_QUERY",
            "id": query_id,
            "timestamp": datetime.now().isoformat(),
            "selector": {
                "type": "xpath",
                "value": xpath,
                "options": {
                    "waitVisible": True,
                    "timeout": int(timeout * 1000)  # Convert to milliseconds
                }
            },
            "actions": [{
                "type": "input",
                "params": {"text": text, "delay": 100}
            }]
        }

        # Send element query
        success = await websocket_server.send_command(
            MessageType.ELEMENT_QUERY, command_data, message_id=query_id
        )

        if not success:
            logger.warning(f"Failed to send input command for xpath: {xpath}")
            return False

        # Wait for response
        result = await wait_for_element_query_result_async(query_id, timeout)
        return result is not None and result.get("success", False)

    except Exception as e:
        logger.error(f"Error inputting text to element by xpath {xpath}: {e}")
        return False
