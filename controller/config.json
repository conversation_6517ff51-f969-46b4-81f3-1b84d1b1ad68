{"server": {"host": "localhost", "port": 8765, "ping_interval": 20, "ping_timeout": 10, "max_clients": 10, "max_message_size": 1048576}, "gui": {"window_width": 2000, "window_height": 800, "auto_scroll": true, "show_timestamps": true, "max_messages": 1000, "theme": "dark", "font_family": "Consolas", "font_size": 9, "language": "zh"}, "logging": {"level": "DEBUG", "file": "logs/controller.log", "max_size": "10MB", "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "console_output": true}, "debug": {"auto_save_interval": 30, "export_format": "json", "include_raw_messages": true, "enable_statistics": true, "performance_monitoring": false}, "auth": {"enabled": true, "api_keys": ["dev-api-key-12345", "production-api-key-67890"], "require_auth_for_websocket": true, "require_auth_for_api": true, "auth_header_name": "Authorization", "auth_scheme": "Bearer"}}