# ChatGPT Controller API Client

A comprehensive Python client library for the ChatGPT Controller API, featuring both command-line interface (CLI) and graphical user interface (GUI) applications.

## Features

- 🚀 **Complete API Coverage**: Full support for all ChatGPT Controller API endpoints
- 💻 **Rich CLI Interface**: Beautiful command-line interface with Rich formatting
- 🖥️ **PyQt6 GUI**: Modern graphical interface for desktop users
- 📱 **Terminal UI (TUI)**: Interactive terminal interface with real-time updates
- 🌍 **Multi-language Support**: English and Chinese (中文) language support
- ⚙️ **Configuration Management**: Flexible configuration with YAML support
- 🔄 **Async Support**: Built with async/await for optimal performance
- 📊 **Real-time Monitoring**: Live status updates and system metrics
- 🎯 **Type Safety**: Full type hints and Pydantic models

## Installation

### Prerequisites

- Python 3.12 or higher
- ChatGPT Controller API server running

### Install Dependencies

```bash
cd client
pip install -e .
```

For development:
```bash
pip install -e ".[dev]"
```

## Quick Start

### CLI Usage

```bash
# Check system status
chatgpt-cli status

# Start a new conversation
chatgpt-cli start "Help me understand quantum computing" --mode research

# List conversations
chatgpt-cli list --status active

# Get conversation details
chatgpt-cli get conv-1234567890

# Send a message
chatgpt-cli send conv-1234567890 "Can you explain this in more detail?"

# Interactive mode
chatgpt-cli start "Hello" --interactive
```

### GUI Usage

```bash
# Launch the GUI application
chatgpt-gui
```

### TUI Usage

```bash
# Launch the Terminal UI
chatgpt-tui

# TUI supports keyboard navigation:
# 1-4: Switch between tabs (Status, Conversations, Config, Help)
# r: Refresh current tab
# l: Toggle language (English/中文)
# q: Quit application
```

### Language Support

The client automatically detects your system language and supports:

```bash
# Set language via CLI
chatgpt-cli language zh    # 设置为中文
chatgpt-cli language en    # Set to English

# Show current language
chatgpt-cli language
```

**Supported Languages:**
- English (en)
- 中文 (zh) - Chinese Simplified

### Python API Usage

```python
import asyncio
from chatgpt_client import ChatGPTClient
from chatgpt_client.models import ChatGPTMode

async def main():
    async with ChatGPTClient() as client:
        # Check status
        status = await client.get_status()
        print(f"Connected: {status.connection_status.connected}")

        # Start a conversation
        result = await client.start_conversation(
            "Help me learn Python",
            mode=ChatGPTMode.RESEARCH
        )
        print(f"Conversation ID: {result.conversation_id}")

        # Send a message
        await client.send_message(
            result.conversation_id,
            "What are the best practices for async programming?"
        )

        # Get conversation details
        conversation = await client.get_conversation(result.conversation_id)
        print(f"Messages: {len(conversation.messages)}")

asyncio.run(main())
```

## Configuration

The client uses a YAML configuration file located at:
- Linux/macOS: `~/.config/chatgpt-controller/config.yaml`
- Windows: `%APPDATA%\chatgpt-controller\config.yaml`

### Default Configuration

```yaml
base_url: "http://localhost:8000"
api_version: "v1"
timeout: 30
cli_output_format: "table"
cli_color: true
gui_theme: "light"
gui_window_size: [1200, 800]
log_level: "INFO"
```

### CLI Configuration Commands

```bash
# Show current configuration
chatgpt-cli config --show

# Set API URL
chatgpt-cli config --url "http://your-api-server:8000"

# Set timeout
chatgpt-cli config --timeout 60

# Reset to defaults
chatgpt-cli config --reset
```

## API Reference

### Client Class

```python
class ChatGPTClient:
    async def get_status() -> StatusResponse
    async def health_check() -> HealthCheckResponse
    async def get_metrics() -> Dict[str, Any]

    async def start_conversation(
        init_prompt: str,
        mode: Optional[ChatGPTMode] = None
    ) -> ConversationStartResponse

    async def list_conversations(
        page: int = 1,
        page_size: int = 50,
        status: Optional[ConversationStatus] = None,
        mode: Optional[ChatGPTMode] = None,
        search: Optional[str] = None,
        created_after: Optional[datetime] = None,
        created_before: Optional[datetime] = None
    ) -> ConversationListResponse

    async def get_conversation(
        conversation_id: str,
        use_cache: bool = True
    ) -> ConversationResponse

    async def send_message(
        conversation_id: str,
        message: str
    ) -> MessageResponse
```

### Models

#### Enums

- `ChatGPTMode`: research, reason, search, canvas, picture_v2
- `ConversationStatus`: active, archived, deleted
- `MessageRole`: user, assistant, system

#### Response Models

- `StatusResponse`: System status and health information
- `ConversationStartResponse`: New conversation details
- `ConversationListResponse`: Paginated conversation list
- `ConversationResponse`: Conversation details with messages
- `MessageResponse`: Message sending result

## CLI Commands

### Status Commands

```bash
# System status
chatgpt-cli status [--json]

# Health check
chatgpt-cli health

# Detailed metrics
chatgpt-cli metrics [--json]
```

### Conversation Commands

```bash
# Start new conversation
chatgpt-cli start <prompt> [--mode MODE] [--interactive]

# List conversations
chatgpt-cli list [--page N] [--page-size N] [--status STATUS]
                 [--mode MODE] [--search TERM] [--json]

# Get conversation details
chatgpt-cli get <conversation-id> [--no-cache] [--no-messages] [--json]

# Send message
chatgpt-cli send <conversation-id> [message] [--interactive]
```

### Configuration Commands

```bash
# Show configuration
chatgpt-cli config [--show]

# Update configuration
chatgpt-cli config [--url URL] [--timeout SECONDS]

# Reset configuration
chatgpt-cli config --reset
```

## GUI Features

### Main Window

- **Status Tab**: Real-time system monitoring with auto-refresh
- **Conversations Tab**: Browse, filter, and manage conversations
- **Menu Bar**: Quick access to all functions
- **Status Bar**: Connection status indicator

### Dialogs

- **Start Conversation**: Create new conversations with mode selection
- **Conversation Details**: View messages and send new ones
- **Configuration**: Manage client settings

### Keyboard Shortcuts

- `F5`: Refresh all data
- `Ctrl+N`: Start new conversation
- `Ctrl+R`: Refresh conversations
- `Ctrl+Q`: Quit application

## Error Handling

The client provides comprehensive error handling with specific exception types:

```python
from chatgpt_client.exceptions import (
    ChatGPTClientError,      # Base exception
    APIError,                # API-related errors
    ValidationError,         # Request validation errors
    ConnectionError,         # Network connection errors
    NotFoundError,          # 404 errors
    ServerError             # 5xx server errors
)

try:
    async with ChatGPTClient() as client:
        result = await client.get_conversation("invalid-id")
except NotFoundError:
    print("Conversation not found")
except ConnectionError:
    print("Cannot connect to API server")
except APIError as e:
    print(f"API error: {e.message}")
```