"""
Tests for configuration management
"""

import pytest
import tempfile
import yaml
from pathlib import Path
from unittest.mock import patch

from chatgpt_client.config import ClientConfig, ConfigManager


class TestClientConfig:
    """Test ClientConfig model"""
    
    def test_default_config(self):
        """Test default configuration values"""
        config = ClientConfig()
        
        assert config.base_url == "http://localhost:8000"
        assert config.api_version == "v1"
        assert config.timeout == 30
        assert config.cli_output_format == "table"
        assert config.cli_color is True
        assert config.gui_theme == "light"
        assert config.log_level == "INFO"
    
    def test_api_base_url_property(self):
        """Test API base URL construction"""
        config = ClientConfig(
            base_url="http://example.com:8080",
            api_version="v2"
        )
        
        assert config.api_base_url == "http://example.com:8080/api/v2"
    
    def test_config_validation(self):
        """Test configuration validation"""
        # Valid config
        config = ClientConfig(timeout=60)
        assert config.timeout == 60
        
        # Invalid timeout (should be clamped or validated by Pydantic)
        with pytest.raises(ValueError):
            ClientConfig(timeout=-1)
    
    def test_config_from_dict(self):
        """Test creating config from dictionary"""
        data = {
            "base_url": "http://test:9000",
            "timeout": 45,
            "cli_color": False
        }
        
        config = ClientConfig(**data)
        assert config.base_url == "http://test:9000"
        assert config.timeout == 45
        assert config.cli_color is False


class TestConfigManager:
    """Test ConfigManager class"""
    
    def test_config_manager_singleton(self):
        """Test that ConfigManager behaves like a singleton"""
        manager1 = ConfigManager()
        manager2 = ConfigManager()
        
        # Should be the same instance
        assert manager1 is manager2
    
    def test_default_config_dir(self):
        """Test default config directory detection"""
        manager = ConfigManager()
        
        # Should use XDG config directory
        assert "chatgpt-controller" in str(manager.config_dir)
        assert manager.config_file.name == "config.yaml"
    
    @patch('chatgpt_client.config.ConfigManager.config_file')
    def test_load_config_file_not_exists(self, mock_config_file):
        """Test loading config when file doesn't exist"""
        # Mock file that doesn't exist
        mock_config_file.exists.return_value = False
        
        manager = ConfigManager()
        config = manager.config
        
        # Should return default config
        assert isinstance(config, ClientConfig)
        assert config.base_url == "http://localhost:8000"
    
    def test_load_config_from_file(self):
        """Test loading config from YAML file"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "config.yaml"
            
            # Create test config file
            test_config = {
                "base_url": "http://test-server:8080",
                "timeout": 60,
                "cli_color": False
            }
            
            with open(config_file, 'w') as f:
                yaml.dump(test_config, f)
            
            # Create manager with custom config file
            manager = ConfigManager()
            manager.config_file = config_file
            manager._config = None  # Reset cached config
            
            config = manager.config
            
            assert config.base_url == "http://test-server:8080"
            assert config.timeout == 60
            assert config.cli_color is False
    
    def test_save_config(self):
        """Test saving config to file"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "config.yaml"
            
            manager = ConfigManager()
            manager.config_file = config_file
            manager._config = None
            
            # Update config
            manager.update_config(
                base_url="http://new-server:9000",
                timeout=120
            )
            
            # Verify file was created and contains correct data
            assert config_file.exists()
            
            with open(config_file, 'r') as f:
                saved_data = yaml.safe_load(f)
            
            assert saved_data["base_url"] == "http://new-server:9000"
            assert saved_data["timeout"] == 120
    
    def test_update_config(self):
        """Test updating configuration"""
        manager = ConfigManager()
        original_url = manager.config.base_url
        
        # Update config
        manager.update_config(
            base_url="http://updated:8080",
            timeout=90
        )
        
        # Verify changes
        config = manager.config
        assert config.base_url == "http://updated:8080"
        assert config.timeout == 90
        
        # Other values should remain unchanged
        assert config.api_version == "v1"  # Default value
    
    def test_config_caching(self):
        """Test that config is cached properly"""
        manager = ConfigManager()
        
        # First access
        config1 = manager.config
        
        # Second access should return same object
        config2 = manager.config
        
        assert config1 is config2
    
    def test_invalid_yaml_handling(self):
        """Test handling of invalid YAML files"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "config.yaml"
            
            # Create invalid YAML file
            with open(config_file, 'w') as f:
                f.write("invalid: yaml: content: [")
            
            manager = ConfigManager()
            manager.config_file = config_file
            manager._config = None
            
            # Should fall back to default config
            config = manager.config
            assert isinstance(config, ClientConfig)
            assert config.base_url == "http://localhost:8000"


if __name__ == "__main__":
    pytest.main([__file__])
