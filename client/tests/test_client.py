"""
Tests for ChatGPT Controller API Client
"""

import pytest
import httpx
from unittest.mock import AsyncMock, patch
from datetime import datetime

from chatgpt_client.client import ChatGPTClient
from chatgpt_client.models import (
    StatusResponse,
    ConnectionStatus,
    SystemHealth,
    ConversationStartResponse,
    ChatGPTMode,
)
from chatgpt_client.exceptions import APIError, ConnectionError as ClientConnectionError
from chatgpt_client.config import ClientConfig


@pytest.fixture
def mock_config():
    """Mock configuration for testing"""
    return ClientConfig(
        base_url="http://test-api:8000",
        timeout=10
    )


@pytest.fixture
def client(mock_config):
    """Create test client"""
    return ChatGPTClient(config=mock_config)


@pytest.mark.asyncio
class TestChatGPTClient:
    """Test cases for ChatGPTClient"""
    
    async def test_client_initialization(self, mock_config):
        """Test client initialization"""
        client = ChatGPTClient(config=mock_config)
        assert client.base_url == "http://test-api:8000/api/v1"
        assert client.timeout == 10
        await client.close()
    
    async def test_client_context_manager(self, mock_config):
        """Test client as context manager"""
        async with ChatGPTClient(config=mock_config) as client:
            assert client.base_url == "http://test-api:8000/api/v1"
    
    @patch('httpx.AsyncClient.request')
    async def test_get_status_success(self, mock_request, client):
        """Test successful status request"""
        # Mock response
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "timestamp": "2024-01-01T12:00:00Z",
            "connection_status": {
                "connected": True,
                "client_count": 2,
                "uptime_seconds": 3600.0
            },
            "system_health": {
                "database_healthy": True,
                "websocket_healthy": True,
                "memory_usage_mb": 256.0,
                "cpu_usage_percent": 15.5
            }
        }
        mock_request.return_value = mock_response
        
        # Test
        result = await client.get_status()
        
        # Assertions
        assert isinstance(result, StatusResponse)
        assert result.success is True
        assert result.connection_status.connected is True
        assert result.connection_status.client_count == 2
        assert result.system_health.database_healthy is True
        
        # Verify request was made correctly
        mock_request.assert_called_once_with(
            method="GET",
            url="/status",
            params=None,
            json=None
        )
        
        await client.close()
    
    @patch('httpx.AsyncClient.request')
    async def test_get_status_api_error(self, mock_request, client):
        """Test API error handling"""
        # Mock error response
        mock_response = AsyncMock()
        mock_response.status_code = 500
        mock_response.json.return_value = {"detail": "Internal server error"}
        mock_request.return_value = mock_response
        
        # Test
        with pytest.raises(APIError) as exc_info:
            await client.get_status()
        
        assert "Server error" in str(exc_info.value)
        assert exc_info.value.status_code == 500
        
        await client.close()
    
    @patch('httpx.AsyncClient.request')
    async def test_start_conversation_success(self, mock_request, client):
        """Test successful conversation start"""
        # Mock response
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "timestamp": "2024-01-01T12:00:00Z",
            "conversation_id": "conv-123456",
            "url": "https://chatgpt.com/c/conv-123456"
        }
        mock_request.return_value = mock_response
        
        # Test
        result = await client.start_conversation(
            "Test prompt",
            mode=ChatGPTMode.RESEARCH
        )
        
        # Assertions
        assert isinstance(result, ConversationStartResponse)
        assert result.conversation_id == "conv-123456"
        assert result.url == "https://chatgpt.com/c/conv-123456"
        
        # Verify request
        mock_request.assert_called_once_with(
            method="POST",
            url="/conversations/start",
            params=None,
            json={
                "init_prompt": "Test prompt",
                "mode": "research"
            }
        )
        
        await client.close()
    
    @patch('httpx.AsyncClient.request')
    async def test_connection_error(self, mock_request, client):
        """Test connection error handling"""
        # Mock connection error
        mock_request.side_effect = httpx.ConnectError("Connection failed")
        
        # Test
        with pytest.raises(ClientConnectionError) as exc_info:
            await client.get_status()
        
        assert "Failed to connect to API" in str(exc_info.value)
        
        await client.close()
    
    @patch('httpx.AsyncClient.request')
    async def test_timeout_error(self, mock_request, client):
        """Test timeout error handling"""
        # Mock timeout error
        mock_request.side_effect = httpx.TimeoutException("Request timeout")
        
        # Test
        with pytest.raises(ClientConnectionError) as exc_info:
            await client.get_status()
        
        assert "Request timeout" in str(exc_info.value)
        
        await client.close()
    
    @patch('httpx.AsyncClient.request')
    async def test_list_conversations_with_filters(self, mock_request, client):
        """Test listing conversations with filters"""
        # Mock response
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "conversations": [],
            "total": 0,
            "page": 1,
            "page_size": 20
        }
        mock_request.return_value = mock_response
        
        # Test
        await client.list_conversations(
            page=2,
            page_size=20,
            search="test query"
        )
        
        # Verify request parameters
        mock_request.assert_called_once_with(
            method="GET",
            url="/conversations",
            params={
                "page": 2,
                "page_size": 20,
                "search": "test query"
            },
            json=None
        )
        
        await client.close()
    
    @patch('httpx.AsyncClient.request')
    async def test_send_message_success(self, mock_request, client):
        """Test successful message sending"""
        # Mock response
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "success": True,
            "message_id": "msg-123",
            "sent_at": "2024-01-01T12:00:00Z"
        }
        mock_request.return_value = mock_response
        
        # Test
        result = await client.send_message("conv-123", "Test message")
        
        # Assertions
        assert result.message_id == "msg-123"
        
        # Verify request
        mock_request.assert_called_once_with(
            method="POST",
            url="/conversations/conv-123/messages",
            params=None,
            json={"message": "Test message"}
        )
        
        await client.close()


@pytest.mark.asyncio
class TestClientConfiguration:
    """Test client configuration handling"""
    
    def test_config_override(self):
        """Test configuration override"""
        client = ChatGPTClient(
            base_url="http://custom:9000",
            timeout=60
        )
        
        assert "http://custom:9000" in client.base_url
        assert client.timeout == 60
    
    def test_config_from_object(self, mock_config):
        """Test configuration from config object"""
        client = ChatGPTClient(config=mock_config)
        
        assert "http://test-api:8000" in client.base_url
        assert client.timeout == 10


if __name__ == "__main__":
    pytest.main([__file__])
