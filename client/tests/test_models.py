"""
Tests for Pydantic models
"""

import pytest
from datetime import datetime
from pydantic import ValidationError

from chatgpt_client.models import (
    ChatGPTMode,
    ConversationStatus,
    MessageRole,
    StatusResponse,
    ConnectionStatus,
    SystemHealth,
    ConversationStartRequest,
    ConversationStartResponse,
    ConversationListResponse,
    ConversationSummary,
    MessageRequest,
    MessageResponse,
    Message,
    Conversation,
    ConversationResponse
)


class TestEnums:
    """Test enum classes"""
    
    def test_chatgpt_mode_enum(self):
        """Test ChatGPTMode enum"""
        assert ChatGPTMode.RESEARCH == "research"
        assert ChatGPTMode.REASON == "reason"
        assert ChatGPTMode.SEARCH == "search"
        assert ChatGPTMode.CANVAS == "canvas"
        assert ChatGPTMode.PICTURE_V2 == "picture_v2"
        
        # Test enum creation from string
        assert ChatGPTMode("research") == ChatGPTMode.RESEARCH
        
        # Test invalid value
        with pytest.raises(ValueError):
            ChatGPTMode("invalid_mode")
    
    def test_conversation_status_enum(self):
        """Test ConversationStatus enum"""
        assert ConversationStatus.ACTIVE == "active"
        assert ConversationStatus.ARCHIVED == "archived"
        assert ConversationStatus.DELETED == "deleted"
    
    def test_message_role_enum(self):
        """Test MessageRole enum"""
        assert MessageRole.USER == "user"
        assert MessageRole.ASSISTANT == "assistant"
        assert MessageRole.SYSTEM == "system"


class TestStatusModels:
    """Test status-related models"""
    
    def test_connection_status(self):
        """Test ConnectionStatus model"""
        data = {
            "connected": True,
            "client_count": 5,
            "uptime_seconds": 3600.5
        }
        
        status = ConnectionStatus(**data)
        assert status.connected is True
        assert status.client_count == 5
        assert status.uptime_seconds == 3600.5
    
    def test_system_health(self):
        """Test SystemHealth model"""
        data = {
            "database_healthy": True,
            "websocket_healthy": False,
            "memory_usage_mb": 512.0,
            "cpu_usage_percent": 25.5
        }
        
        health = SystemHealth(**data)
        assert health.database_healthy is True
        assert health.websocket_healthy is False
        assert health.memory_usage_mb == 512.0
        assert health.cpu_usage_percent == 25.5
    
    def test_status_response(self):
        """Test StatusResponse model"""
        data = {
            "success": True,
            "timestamp": "2024-01-01T12:00:00Z",
            "connection_status": {
                "connected": True,
                "client_count": 3,
                "uptime_seconds": 1800.0
            },
            "system_health": {
                "database_healthy": True,
                "websocket_healthy": True,
                "memory_usage_mb": 256.0,
                "cpu_usage_percent": 15.0
            }
        }
        
        response = StatusResponse(**data)
        assert response.success is True
        assert isinstance(response.timestamp, datetime)
        assert isinstance(response.connection_status, ConnectionStatus)
        assert isinstance(response.system_health, SystemHealth)


class TestConversationModels:
    """Test conversation-related models"""
    
    def test_conversation_start_request(self):
        """Test ConversationStartRequest model"""
        # With mode
        request = ConversationStartRequest(
            init_prompt="Test prompt",
            mode=ChatGPTMode.RESEARCH
        )
        assert request.init_prompt == "Test prompt"
        assert request.mode == ChatGPTMode.RESEARCH
        
        # Without mode
        request = ConversationStartRequest(init_prompt="Test prompt")
        assert request.mode is None
        
        # Empty prompt should fail validation
        with pytest.raises(ValidationError):
            ConversationStartRequest(init_prompt="")
    
    def test_conversation_start_response(self):
        """Test ConversationStartResponse model"""
        data = {
            "success": True,
            "timestamp": "2024-01-01T12:00:00Z",
            "conversation_id": "conv-123456",
            "url": "https://chatgpt.com/c/conv-123456"
        }
        
        response = ConversationStartResponse(**data)
        assert response.conversation_id == "conv-123456"
        assert response.url == "https://chatgpt.com/c/conv-123456"
    
    def test_conversation_summary(self):
        """Test ConversationSummary model"""
        data = {
            "id": "conv-123",
            "title": "Test Conversation",
            "mode": "research",
            "status": "active",
            "message_count": 5,
            "created_at": "2024-01-01T12:00:00Z",
            "updated_at": "2024-01-01T13:00:00Z"
        }
        
        summary = ConversationSummary(**data)
        assert summary.id == "conv-123"
        assert summary.title == "Test Conversation"
        assert summary.mode == ChatGPTMode.RESEARCH
        assert summary.status == ConversationStatus.ACTIVE
        assert summary.message_count == 5
        assert isinstance(summary.created_at, datetime)
        assert isinstance(summary.updated_at, datetime)
    
    def test_conversation_list_response(self):
        """Test ConversationListResponse model"""
        data = {
            "success": True,
            "conversations": [
                {
                    "id": "conv-1",
                    "title": "Conv 1",
                    "status": "active",
                    "message_count": 3,
                    "created_at": "2024-01-01T12:00:00Z"
                }
            ],
            "total": 1,
            "page": 1,
            "page_size": 20
        }
        
        response = ConversationListResponse(**data)
        assert len(response.conversations) == 1
        assert response.total == 1
        assert response.page == 1
        assert isinstance(response.conversations[0], ConversationSummary)


class TestMessageModels:
    """Test message-related models"""
    
    def test_message_request(self):
        """Test MessageRequest model"""
        request = MessageRequest(message="Hello, world!")
        assert request.message == "Hello, world!"
        
        # Empty message should fail validation
        with pytest.raises(ValidationError):
            MessageRequest(message="")
    
    def test_message_response(self):
        """Test MessageResponse model"""
        data = {
            "success": True,
            "timestamp": "2024-01-01T12:00:00Z",
            "message_id": "msg-123",
            "sent_at": "2024-01-01T12:00:00Z"
        }
        
        response = MessageResponse(**data)
        assert response.message_id == "msg-123"
        assert isinstance(response.sent_at, datetime)
    
    def test_message_model(self):
        """Test Message model"""
        data = {
            "id": "msg-123",
            "role": "user",
            "content": "Hello, ChatGPT!",
            "created_at": "2024-01-01T12:00:00Z"
        }
        
        message = Message(**data)
        assert message.id == "msg-123"
        assert message.role == MessageRole.USER
        assert message.content == "Hello, ChatGPT!"
        assert isinstance(message.created_at, datetime)
    
    def test_conversation_model(self):
        """Test Conversation model"""
        data = {
            "id": "conv-123",
            "title": "Test Conversation",
            "mode": "research",
            "status": "active",
            "init_prompt": "Initial prompt",
            "message_count": 2,
            "created_at": "2024-01-01T12:00:00Z",
            "updated_at": "2024-01-01T13:00:00Z",
            "is_cached": True
        }
        
        conversation = Conversation(**data)
        assert conversation.id == "conv-123"
        assert conversation.mode == ChatGPTMode.RESEARCH
        assert conversation.status == ConversationStatus.ACTIVE
        assert conversation.is_cached is True
    
    def test_conversation_response(self):
        """Test ConversationResponse model"""
        data = {
            "success": True,
            "conversation": {
                "id": "conv-123",
                "title": "Test",
                "status": "active",
                "message_count": 1,
                "created_at": "2024-01-01T12:00:00Z",
                "is_cached": False
            },
            "messages": [
                {
                    "id": "msg-1",
                    "role": "user",
                    "content": "Hello",
                    "created_at": "2024-01-01T12:00:00Z"
                }
            ]
        }
        
        response = ConversationResponse(**data)
        assert isinstance(response.conversation, Conversation)
        assert len(response.messages) == 1
        assert isinstance(response.messages[0], Message)


class TestModelValidation:
    """Test model validation edge cases"""
    
    def test_optional_fields(self):
        """Test models with optional fields"""
        # ConversationSummary with minimal data
        summary = ConversationSummary(
            id="conv-123",
            status="active",
            message_count=0,
            created_at="2024-01-01T12:00:00Z"
        )
        assert summary.title is None
        assert summary.mode is None
        assert summary.updated_at is None
    
    def test_datetime_parsing(self):
        """Test datetime field parsing"""
        # ISO format
        summary = ConversationSummary(
            id="conv-123",
            status="active",
            message_count=0,
            created_at="2024-01-01T12:00:00.123456Z"
        )
        assert isinstance(summary.created_at, datetime)
        
        # Invalid datetime should fail
        with pytest.raises(ValidationError):
            ConversationSummary(
                id="conv-123",
                status="active",
                message_count=0,
                created_at="invalid-date"
            )


if __name__ == "__main__":
    pytest.main([__file__])
