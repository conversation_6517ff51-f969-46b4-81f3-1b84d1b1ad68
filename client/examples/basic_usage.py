#!/usr/bin/env python3
"""
Basic usage example for ChatGPT Controller API Client

This script demonstrates how to use the ChatGPT Controller API client
to interact with the API programmatically.
"""

import asyncio
import sys
from datetime import datetime

from chatgpt_client import ChatGPTClient
from chatgpt_client.models import Chat<PERSON>TMode, ConversationStatus
from chatgpt_client.exceptions import ChatGPTClientError


async def check_system_status():
    """Check and display system status"""
    print("🔍 Checking system status...")
    
    async with ChatGPTClient() as client:
        try:
            status = await client.get_status()
            
            print(f"✅ System Status: {'Healthy' if status.success else 'Unhealthy'}")
            print(f"🔗 Connected: {status.connection_status.connected}")
            print(f"👥 Active Clients: {status.connection_status.client_count}")
            print(f"⏱️  Uptime: {status.connection_status.uptime_seconds:.1f}s")
            print(f"💾 Database: {'Healthy' if status.system_health.database_healthy else 'Unhealthy'}")
            print(f"🌐 WebSocket: {'Healthy' if status.system_health.websocket_healthy else 'Unhealthy'}")
            print(f"🧠 Memory: {status.system_health.memory_usage_mb:.1f} MB")
            print(f"⚡ CPU: {status.system_health.cpu_usage_percent:.1f}%")
            
            return status.success
            
        except ChatGPTClientError as e:
            print(f"❌ Error checking status: {e.message}")
            return False


async def start_conversation_example():
    """Example of starting a new conversation"""
    print("\n🚀 Starting a new conversation...")
    
    async with ChatGPTClient() as client:
        try:
            # Start a conversation with research mode
            result = await client.start_conversation(
                init_prompt="Help me understand the basics of machine learning",
                mode=ChatGPTMode.RESEARCH
            )
            
            print(f"✅ Conversation started successfully!")
            print(f"🆔 ID: {result.conversation_id}")
            print(f"🔗 URL: {result.url}")
            
            return result.conversation_id
            
        except ChatGPTClientError as e:
            print(f"❌ Error starting conversation: {e.message}")
            return None


async def send_message_example(conversation_id: str):
    """Example of sending a message to a conversation"""
    print(f"\n💬 Sending message to conversation {conversation_id[:20]}...")
    
    async with ChatGPTClient() as client:
        try:
            result = await client.send_message(
                conversation_id,
                "Can you explain what supervised learning is?"
            )
            
            print(f"✅ Message sent successfully!")
            print(f"🆔 Message ID: {result.message_id}")
            print(f"⏰ Sent at: {result.sent_at.strftime('%Y-%m-%d %H:%M:%S')}")
            
        except ChatGPTClientError as e:
            print(f"❌ Error sending message: {e.message}")


async def list_conversations_example():
    """Example of listing conversations"""
    print("\n📋 Listing recent conversations...")
    
    async with ChatGPTClient() as client:
        try:
            result = await client.list_conversations(
                page=1,
                page_size=5,
                status=ConversationStatus.ACTIVE
            )
            
            print(f"📊 Found {result.total} total conversations")
            print(f"📄 Showing page {result.page} ({len(result.conversations)} conversations)")
            
            if result.conversations:
                print("\n📝 Recent conversations:")
                for conv in result.conversations:
                    title = conv.title or "Untitled"
                    if len(title) > 40:
                        title = title[:37] + "..."
                    
                    print(f"  • {title}")
                    print(f"    ID: {conv.id}")
                    print(f"    Mode: {conv.mode or 'N/A'}")
                    print(f"    Messages: {conv.message_count}")
                    print(f"    Created: {conv.created_at.strftime('%Y-%m-%d %H:%M')}")
                    print()
            else:
                print("📭 No conversations found")
                
            return result.conversations
            
        except ChatGPTClientError as e:
            print(f"❌ Error listing conversations: {e.message}")
            return []


async def get_conversation_details_example(conversation_id: str):
    """Example of getting conversation details"""
    print(f"\n🔍 Getting details for conversation {conversation_id[:20]}...")
    
    async with ChatGPTClient() as client:
        try:
            result = await client.get_conversation(conversation_id)
            
            conv = result.conversation
            print(f"📋 Conversation Details:")
            print(f"  Title: {conv.title or 'Untitled'}")
            print(f"  Mode: {conv.mode or 'N/A'}")
            print(f"  Status: {conv.status}")
            print(f"  Messages: {conv.message_count}")
            print(f"  Created: {conv.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"  Cached: {'Yes' if conv.is_cached else 'No'}")
            
            if conv.init_prompt:
                print(f"\n💭 Initial Prompt:")
                print(f"  {conv.init_prompt}")
            
            if result.messages:
                print(f"\n💬 Messages ({len(result.messages)}):")
                for i, msg in enumerate(result.messages[-3:], 1):  # Show last 3 messages
                    role_emoji = "👤" if msg.role == "user" else "🤖"
                    print(f"  {role_emoji} {msg.role.title()}: {msg.content[:100]}...")
                    print(f"    {msg.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
                    print()
            
        except ChatGPTClientError as e:
            print(f"❌ Error getting conversation: {e.message}")


async def health_check_example():
    """Example of performing a health check"""
    print("\n🏥 Performing health check...")
    
    async with ChatGPTClient() as client:
        try:
            health = await client.health_check()
            
            print(f"🎯 Overall Status: {health.status}")
            print(f"⏱️  Uptime: {health.uptime_seconds:.1f}s")
            
            print("\n🔧 Component Health:")
            for component, status in health.checks.items():
                status_emoji = "✅" if status else "❌"
                print(f"  {status_emoji} {component}: {status}")
            
        except ChatGPTClientError as e:
            print(f"❌ Error performing health check: {e.message}")


async def main():
    """Main example function"""
    print("🎯 ChatGPT Controller API Client - Basic Usage Example")
    print("=" * 60)
    
    # Check if system is available
    if not await check_system_status():
        print("\n❌ System is not available. Please check your API server.")
        return 1
    
    # Perform health check
    await health_check_example()
    
    # List existing conversations
    conversations = await list_conversations_example()
    
    # Start a new conversation
    conversation_id = await start_conversation_example()
    
    if conversation_id:
        # Send a message to the new conversation
        await send_message_example(conversation_id)
        
        # Get conversation details
        await get_conversation_details_example(conversation_id)
    
    # If we have existing conversations, show details of the first one
    elif conversations:
        await get_conversation_details_example(conversations[0].id)
    
    print("\n✨ Example completed successfully!")
    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Example interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
