#!/usr/bin/env python3
"""
Test script for internationalization features
"""

from chatgpt_client.i18n import Language, set_language, t, detect_language

def test_language_support():
    """Test language switching and translations"""
    print("🌍 Testing Language Support")
    print("=" * 50)
    
    # Test English
    print("\n📍 Testing English:")
    set_language(Language.EN)
    print(f"Language: {Language.EN.value}")
    print(f"System Status: {t('system_status')}")
    print(f"Conversations: {t('conversations')}")
    print(f"Send Message: {t('send_message')}")
    print(f"Configuration: {t('configuration')}")
    print(f"Help: {t('help')}")
    
    # Test Chinese
    print("\n📍 Testing Chinese:")
    set_language(Language.ZH)
    print(f"Language: {Language.ZH.value}")
    print(f"System Status: {t('system_status')}")
    print(f"Conversations: {t('conversations')}")
    print(f"Send Message: {t('send_message')}")
    print(f"Configuration: {t('configuration')}")
    print(f"Help: {t('help')}")
    
    # Test language detection
    print("\n📍 Testing Language Detection:")
    detected = detect_language()
    print(f"Detected language: {detected.value}")
    
    # Test missing key
    print("\n📍 Testing Missing Key:")
    set_language(Language.EN)
    print(f"Missing key: {t('nonexistent_key')}")
    
    print("\n✅ Language support test completed!")

if __name__ == "__main__":
    test_language_support()
