[project]
name = "chatgpt-controller-client"
version = "0.1.0"
description = "API client for ChatGPT Controller with CLI and GUI interfaces"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "httpx>=0.25.0",
    "pydantic>=2.0.0",
    "rich>=13.0.0",
    "typer>=0.9.0",
    "PyQt6>=6.5.0",
    "pyyaml>=6.0",
    "python-dateutil>=2.8.0",
    "asyncio-mqtt>=0.13.0",
]

[project.scripts]
chatgpt-cli = "chatgpt_client.cli:app"
chatgpt-gui = "chatgpt_client.gui:main"

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.10.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py312']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
