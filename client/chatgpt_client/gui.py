"""
PyQt6 GUI Application for ChatGPT Controller API Client

Provides a graphical interface for all API operations including conversation management,
status monitoring, and real-time updates.
"""

import sys
import asyncio
from datetime import datetime
from typing import Optional, List
from concurrent.futures import Thread<PERSON>oolExecutor

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QTableWidget, QTableWidgetItem, QTextEdit, QLineEdit,
    QPushButton, QLabel, QComboBox, QSpinBox, QCheckBox, QSplitter,
    QGroupBox, QFormLayout, QMessageBox, QProgressBar, QStatusBar,
    QHeaderView, QAbstractItemView, QDialog, QDialogButtonBox
)
from PyQt6.QtCore import QThread, pyqtSignal, QTimer, Qt, QSize
from PyQt6.QtGui import <PERSON><PERSON>ont, QIcon, QPixmap, QAction

from .client import ChatGPTClient
from .models import Chat<PERSON><PERSON><PERSON>, ConversationStatus
from .config import config_manager
from .exceptions import ChatGPTClientError


class AsyncWorker(QThread):
    """Worker thread for async operations"""
    
    finished = pyqtSignal(object)
    error = pyqtSignal(str)
    
    def __init__(self, coro):
        super().__init__()
        self.coro = coro
    
    def run(self):
        try:
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(self.coro)
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))
        finally:
            loop.close()


class StatusWidget(QWidget):
    """Widget for displaying system status"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_timer()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Status group
        status_group = QGroupBox("System Status")
        status_layout = QFormLayout()
        
        self.connection_label = QLabel("🔴 Disconnected")
        self.database_label = QLabel("❓ Unknown")
        self.websocket_label = QLabel("❓ Unknown")
        self.uptime_label = QLabel("N/A")
        self.memory_label = QLabel("N/A")
        self.cpu_label = QLabel("N/A")
        
        status_layout.addRow("Connection:", self.connection_label)
        status_layout.addRow("Database:", self.database_label)
        status_layout.addRow("WebSocket:", self.websocket_label)
        status_layout.addRow("Uptime:", self.uptime_label)
        status_layout.addRow("Memory:", self.memory_label)
        status_layout.addRow("CPU:", self.cpu_label)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        # Refresh button
        self.refresh_btn = QPushButton("Refresh Status")
        self.refresh_btn.clicked.connect(self.refresh_status)
        layout.addWidget(self.refresh_btn)
        
        layout.addStretch()
        self.setLayout(layout)
    
    def setup_timer(self):
        """Setup auto-refresh timer"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_status)
        self.timer.start(30000)  # Refresh every 30 seconds
        
        # Initial refresh
        self.refresh_status()
    
    def refresh_status(self):
        """Refresh system status"""
        async def get_status():
            async with ChatGPTClient() as client:
                return await client.get_status()
        
        self.worker = AsyncWorker(get_status())
        self.worker.finished.connect(self.update_status)
        self.worker.error.connect(self.handle_error)
        self.worker.start()
        
        self.refresh_btn.setEnabled(False)
        self.refresh_btn.setText("Refreshing...")
    
    def update_status(self, status_data):
        """Update status display"""
        conn = status_data.connection_status
        health = status_data.system_health
        
        # Connection status
        if conn.connected:
            self.connection_label.setText(f"🟢 Connected ({conn.client_count} clients)")
        else:
            self.connection_label.setText("🔴 Disconnected")
        
        # Health status
        self.database_label.setText("🟢 Healthy" if health.database_healthy else "🔴 Unhealthy")
        self.websocket_label.setText("🟢 Healthy" if health.websocket_healthy else "🔴 Unhealthy")
        
        # System metrics
        self.uptime_label.setText(f"{conn.uptime_seconds:.1f}s")
        self.memory_label.setText(f"{health.memory_usage_mb:.1f} MB")
        self.cpu_label.setText(f"{health.cpu_usage_percent:.1f}%")
        
        self.refresh_btn.setEnabled(True)
        self.refresh_btn.setText("Refresh Status")
    
    def handle_error(self, error_msg):
        """Handle errors"""
        self.connection_label.setText("❌ Error")
        self.refresh_btn.setEnabled(True)
        self.refresh_btn.setText("Refresh Status")
        QMessageBox.warning(self, "Status Error", f"Failed to get status: {error_msg}")


class ConversationListWidget(QWidget):
    """Widget for listing and managing conversations"""
    
    def __init__(self):
        super().__init__()
        self.conversations = []
        self.init_ui()
        self.refresh_conversations()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # Filters
        filter_group = QGroupBox("Filters")
        filter_layout = QHBoxLayout()
        
        self.status_combo = QComboBox()
        self.status_combo.addItems(["All", "active", "archived", "deleted"])
        
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["All", "research", "reason", "search", "canvas", "picture_v2"])
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Search conversations...")
        
        self.page_spin = QSpinBox()
        self.page_spin.setMinimum(1)
        self.page_spin.setValue(1)
        
        self.page_size_spin = QSpinBox()
        self.page_size_spin.setMinimum(1)
        self.page_size_spin.setMaximum(100)
        self.page_size_spin.setValue(20)
        
        refresh_btn = QPushButton("Refresh")
        refresh_btn.clicked.connect(self.refresh_conversations)
        
        filter_layout.addWidget(QLabel("Status:"))
        filter_layout.addWidget(self.status_combo)
        filter_layout.addWidget(QLabel("Mode:"))
        filter_layout.addWidget(self.mode_combo)
        filter_layout.addWidget(QLabel("Search:"))
        filter_layout.addWidget(self.search_edit)
        filter_layout.addWidget(QLabel("Page:"))
        filter_layout.addWidget(self.page_spin)
        filter_layout.addWidget(QLabel("Size:"))
        filter_layout.addWidget(self.page_size_spin)
        filter_layout.addWidget(refresh_btn)
        
        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)
        
        # Conversations table
        self.table = QTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels([
            "ID", "Title", "Mode", "Status", "Messages", "Created"
        ])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.doubleClicked.connect(self.view_conversation)
        
        layout.addWidget(self.table)
        
        # Action buttons
        button_layout = QHBoxLayout()
        
        view_btn = QPushButton("View Details")
        view_btn.clicked.connect(self.view_conversation)
        
        start_btn = QPushButton("Start New")
        start_btn.clicked.connect(self.start_conversation)
        
        button_layout.addWidget(view_btn)
        button_layout.addWidget(start_btn)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def refresh_conversations(self):
        """Refresh conversations list"""
        # Get filter values
        status = self.status_combo.currentText()
        if status == "All":
            status = None
        else:
            status = ConversationStatus(status)
        
        mode = self.mode_combo.currentText()
        if mode == "All":
            mode = None
        else:
            mode = ChatGPTMode(mode)
        
        search = self.search_edit.text().strip() or None
        page = self.page_spin.value()
        page_size = self.page_size_spin.value()
        
        async def get_conversations():
            async with ChatGPTClient() as client:
                return await client.list_conversations(
                    page=page,
                    page_size=page_size,
                    status=status,
                    mode=mode,
                    search=search
                )
        
        self.worker = AsyncWorker(get_conversations())
        self.worker.finished.connect(self.update_conversations)
        self.worker.error.connect(self.handle_error)
        self.worker.start()
    
    def update_conversations(self, result):
        """Update conversations table"""
        self.conversations = result.conversations
        
        self.table.setRowCount(len(self.conversations))
        
        for row, conv in enumerate(self.conversations):
            self.table.setItem(row, 0, QTableWidgetItem(conv.id[:20] + "..."))
            self.table.setItem(row, 1, QTableWidgetItem(conv.title or "Untitled"))
            self.table.setItem(row, 2, QTableWidgetItem(conv.mode or "N/A"))
            self.table.setItem(row, 3, QTableWidgetItem(conv.status))
            self.table.setItem(row, 4, QTableWidgetItem(str(conv.message_count)))
            self.table.setItem(row, 5, QTableWidgetItem(
                conv.created_at.strftime("%Y-%m-%d %H:%M")
            ))
    
    def view_conversation(self):
        """View selected conversation details"""
        current_row = self.table.currentRow()
        if current_row >= 0 and current_row < len(self.conversations):
            conv = self.conversations[current_row]
            dialog = ConversationDetailDialog(conv.id, self)
            dialog.exec()
    
    def start_conversation(self):
        """Start a new conversation"""
        dialog = StartConversationDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.refresh_conversations()
    
    def handle_error(self, error_msg):
        """Handle errors"""
        QMessageBox.warning(self, "Error", f"Failed to load conversations: {error_msg}")


class StartConversationDialog(QDialog):
    """Dialog for starting a new conversation"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Start New Conversation")
        self.setModal(True)
        self.resize(500, 300)
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # Form
        form_layout = QFormLayout()

        self.prompt_edit = QTextEdit()
        self.prompt_edit.setPlaceholderText("Enter your initial prompt...")
        self.prompt_edit.setMaximumHeight(150)

        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["None", "research", "reason", "search", "canvas", "picture_v2"])

        form_layout.addRow("Initial Prompt:", self.prompt_edit)
        form_layout.addRow("Mode:", self.mode_combo)

        layout.addLayout(form_layout)

        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.start_conversation)
        button_box.rejected.connect(self.reject)

        layout.addWidget(button_box)
        self.setLayout(layout)

    def start_conversation(self):
        """Start the conversation"""
        prompt = self.prompt_edit.toPlainText().strip()
        if not prompt:
            QMessageBox.warning(self, "Error", "Please enter an initial prompt")
            return

        mode = self.mode_combo.currentText()
        if mode == "None":
            mode = None
        else:
            mode = ChatGPTMode(mode)

        async def start():
            async with ChatGPTClient() as client:
                return await client.start_conversation(prompt, mode)

        self.worker = AsyncWorker(start())
        self.worker.finished.connect(self.on_success)
        self.worker.error.connect(self.on_error)
        self.worker.start()

        # Disable form while processing
        self.setEnabled(False)

    def on_success(self, result):
        """Handle successful conversation start"""
        self.setEnabled(True)
        QMessageBox.information(
            self,
            "Success",
            f"Conversation started successfully!\nID: {result.conversation_id}"
        )
        self.accept()

    def on_error(self, error_msg):
        """Handle error"""
        self.setEnabled(True)
        QMessageBox.warning(self, "Error", f"Failed to start conversation: {error_msg}")


class ConversationDetailDialog(QDialog):
    """Dialog for viewing conversation details"""

    def __init__(self, conversation_id: str, parent=None):
        super().__init__(parent)
        self.conversation_id = conversation_id
        self.setWindowTitle(f"Conversation: {conversation_id}")
        self.setModal(True)
        self.resize(800, 600)
        self.init_ui()
        self.load_conversation()

    def init_ui(self):
        layout = QVBoxLayout()

        # Conversation info
        self.info_label = QLabel("Loading...")
        layout.addWidget(self.info_label)

        # Messages
        self.messages_edit = QTextEdit()
        self.messages_edit.setReadOnly(True)
        layout.addWidget(self.messages_edit)

        # Send message section
        send_group = QGroupBox("Send Message")
        send_layout = QVBoxLayout()

        self.message_edit = QTextEdit()
        self.message_edit.setMaximumHeight(100)
        self.message_edit.setPlaceholderText("Type your message here...")

        self.send_btn = QPushButton("Send Message")
        self.send_btn.clicked.connect(self.send_message)

        send_layout.addWidget(self.message_edit)
        send_layout.addWidget(self.send_btn)
        send_group.setLayout(send_layout)

        layout.addWidget(send_group)

        # Close button
        close_btn = QPushButton("Close")
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)

        self.setLayout(layout)

    def load_conversation(self):
        """Load conversation details"""
        async def get_conversation():
            async with ChatGPTClient() as client:
                return await client.get_conversation(self.conversation_id)

        self.worker = AsyncWorker(get_conversation())
        self.worker.finished.connect(self.update_conversation)
        self.worker.error.connect(self.handle_error)
        self.worker.start()

    def update_conversation(self, result):
        """Update conversation display"""
        conv = result.conversation

        # Update info
        info_text = f"""
<b>ID:</b> {conv.id}<br>
<b>Title:</b> {conv.title or 'Untitled'}<br>
<b>Mode:</b> {conv.mode or 'N/A'}<br>
<b>Status:</b> {conv.status}<br>
<b>Created:</b> {conv.created_at.strftime('%Y-%m-%d %H:%M:%S')}<br>
<b>Messages:</b> {conv.message_count}<br>
<b>Cached:</b> {'Yes' if conv.is_cached else 'No'}
        """
        self.info_label.setText(info_text)

        # Update messages
        messages_html = ""
        for msg in result.messages:
            role_color = "#0066cc" if msg.role == "user" else "#009900"
            role_emoji = "👤" if msg.role == "user" else "🤖"

            messages_html += f"""
<div style="margin: 10px 0; padding: 10px; border-left: 3px solid {role_color};">
    <b style="color: {role_color};">{role_emoji} {msg.role.title()}</b>
    <small style="color: #666; margin-left: 10px;">
        {msg.created_at.strftime('%Y-%m-%d %H:%M:%S')}
    </small>
    <div style="margin-top: 5px;">{msg.content}</div>
</div>
            """

        self.messages_edit.setHtml(messages_html)

    def send_message(self):
        """Send a message"""
        message = self.message_edit.toPlainText().strip()
        if not message:
            QMessageBox.warning(self, "Error", "Please enter a message")
            return

        async def send():
            async with ChatGPTClient() as client:
                return await client.send_message(self.conversation_id, message)

        self.worker = AsyncWorker(send())
        self.worker.finished.connect(self.on_message_sent)
        self.worker.error.connect(self.handle_error)
        self.worker.start()

        self.send_btn.setEnabled(False)
        self.send_btn.setText("Sending...")

    def on_message_sent(self, result):
        """Handle successful message send"""
        self.send_btn.setEnabled(True)
        self.send_btn.setText("Send Message")
        self.message_edit.clear()

        QMessageBox.information(self, "Success", "Message sent successfully!")

        # Reload conversation to show new message
        self.load_conversation()

    def handle_error(self, error_msg):
        """Handle errors"""
        self.send_btn.setEnabled(True)
        self.send_btn.setText("Send Message")
        QMessageBox.warning(self, "Error", error_msg)


class MainWindow(QMainWindow):
    """Main application window"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("ChatGPT Controller Client")
        self.setGeometry(100, 100, 1200, 800)
        self.init_ui()
        self.init_menu()
        self.init_status_bar()

    def init_ui(self):
        """Initialize the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create tab widget
        self.tab_widget = QTabWidget()

        # Add tabs
        self.status_widget = StatusWidget()
        self.conversations_widget = ConversationListWidget()

        self.tab_widget.addTab(self.status_widget, "📊 Status")
        self.tab_widget.addTab(self.conversations_widget, "💬 Conversations")

        # Layout
        layout = QVBoxLayout()
        layout.addWidget(self.tab_widget)
        central_widget.setLayout(layout)

    def init_menu(self):
        """Initialize menu bar"""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu("&File")

        refresh_action = QAction("&Refresh All", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_all)
        file_menu.addAction(refresh_action)

        file_menu.addSeparator()

        exit_action = QAction("E&xit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Conversation menu
        conv_menu = menubar.addMenu("&Conversation")

        start_action = QAction("&Start New", self)
        start_action.setShortcut("Ctrl+N")
        start_action.triggered.connect(self.start_conversation)
        conv_menu.addAction(start_action)

        refresh_conv_action = QAction("&Refresh List", self)
        refresh_conv_action.setShortcut("Ctrl+R")
        refresh_conv_action.triggered.connect(self.refresh_conversations)
        conv_menu.addAction(refresh_conv_action)

        # Tools menu
        tools_menu = menubar.addMenu("&Tools")

        config_action = QAction("&Configuration", self)
        config_action.triggered.connect(self.show_config)
        tools_menu.addAction(config_action)

        # Help menu
        help_menu = menubar.addMenu("&Help")

        about_action = QAction("&About", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def init_status_bar(self):
        """Initialize status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # Add permanent widgets
        self.connection_status = QLabel("🔴 Disconnected")
        self.status_bar.addPermanentWidget(self.connection_status)

        self.status_bar.showMessage("Ready")

    def refresh_all(self):
        """Refresh all data"""
        self.status_widget.refresh_status()
        self.conversations_widget.refresh_conversations()
        self.status_bar.showMessage("Refreshing all data...")

    def start_conversation(self):
        """Start a new conversation"""
        dialog = StartConversationDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.conversations_widget.refresh_conversations()

    def refresh_conversations(self):
        """Refresh conversations list"""
        self.conversations_widget.refresh_conversations()

    def show_config(self):
        """Show configuration dialog"""
        dialog = ConfigDialog(self)
        dialog.exec()

    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About ChatGPT Controller Client",
            """
<h3>ChatGPT Controller Client</h3>
<p>Version 0.1.0</p>
<p>A Python client for the ChatGPT Controller API with CLI and GUI interfaces.</p>
<p>Built with PyQt6 and Rich.</p>
            """
        )


class ConfigDialog(QDialog):
    """Configuration dialog"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Configuration")
        self.setModal(True)
        self.resize(400, 300)
        self.init_ui()
        self.load_config()

    def init_ui(self):
        layout = QVBoxLayout()

        # Form
        form_layout = QFormLayout()

        self.base_url_edit = QLineEdit()
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setMinimum(1)
        self.timeout_spin.setMaximum(300)
        self.timeout_spin.setSuffix(" seconds")

        form_layout.addRow("Base URL:", self.base_url_edit)
        form_layout.addRow("Timeout:", self.timeout_spin)

        layout.addLayout(form_layout)

        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok |
            QDialogButtonBox.StandardButton.Cancel |
            QDialogButtonBox.StandardButton.RestoreDefaults
        )
        button_box.accepted.connect(self.save_config)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.StandardButton.RestoreDefaults).clicked.connect(self.reset_config)

        layout.addWidget(button_box)
        self.setLayout(layout)

    def load_config(self):
        """Load current configuration"""
        config = config_manager.config
        self.base_url_edit.setText(config.base_url)
        self.timeout_spin.setValue(config.timeout)

    def save_config(self):
        """Save configuration"""
        config_manager.update_config(
            base_url=self.base_url_edit.text(),
            timeout=self.timeout_spin.value()
        )
        QMessageBox.information(self, "Success", "Configuration saved successfully!")
        self.accept()

    def reset_config(self):
        """Reset to default configuration"""
        if QMessageBox.question(
            self,
            "Reset Configuration",
            "Are you sure you want to reset to default configuration?"
        ) == QMessageBox.StandardButton.Yes:
            config_manager.config_file.unlink(missing_ok=True)
            config_manager._config = None
            self.load_config()


def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    app.setApplicationName("ChatGPT Controller Client")
    app.setApplicationVersion("0.1.0")

    # Set application style
    app.setStyle("Fusion")

    # Create and show main window
    window = MainWindow()
    window.show()

    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
