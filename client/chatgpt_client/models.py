"""
Data models for ChatGPT Controller API Client

Based on the OpenAPI schema from the ChatGPT Controller API.
"""

from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field


# Enums
class ChatGPTMode(str, Enum):
    """ChatGPT conversation modes"""
    RESEARCH = "research"
    REASON = "reason"
    SEARCH = "search"
    CANVAS = "canvas"
    PICTURE_V2 = "picture_v2"


class ConversationStatus(str, Enum):
    """Conversation status values"""
    ACTIVE = "active"
    ARCHIVED = "archived"
    DELETED = "deleted"


class MessageRole(str, Enum):
    """Message role values"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


# Base Response Models
class BaseResponse(BaseModel):
    """Base response model"""
    success: bool = True
    message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)


# Connection and Health Models
class ConnectionStatus(BaseModel):
    """WebSocket connection status"""
    connected: bool
    client_count: int
    last_ping: Optional[datetime] = None
    uptime_seconds: float


class SystemHealth(BaseModel):
    """System health metrics"""
    database_healthy: bool
    websocket_healthy: bool
    memory_usage_mb: float
    cpu_usage_percent: float


class HealthCheckResponse(BaseModel):
    """Health check response"""
    status: str = "healthy"
    timestamp: datetime = Field(default_factory=datetime.now)
    version: str = "1.0.0"
    uptime_seconds: float
    checks: Dict[str, bool]


class StatusResponse(BaseResponse):
    """System status response"""
    connection_status: ConnectionStatus
    system_health: SystemHealth


# Message Models
class MessageData(BaseModel):
    """Message data model"""
    id: int
    conversation_id: str
    role: MessageRole
    content: str
    content_type: str = "text"
    message_id: Optional[str] = None
    parent_id: Optional[str] = None
    model: Optional[str] = None
    created_at: datetime
    sent_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None


class MessageRequest(BaseModel):
    """Request to send a message to conversation"""
    message: str = Field(..., min_length=1, max_length=10000)
    
    class Config:
        json_schema_extra = {
            "example": {
                "message": "Can you explain this in more detail?"
            }
        }


class MessageResponse(BaseResponse):
    """Response for message sending"""
    message_id: Optional[str] = None
    sent_at: datetime


# Conversation Models
class ConversationData(BaseModel):
    """Conversation data model"""
    id: str
    title: Optional[str] = None
    mode: Optional[str] = None
    init_prompt: Optional[str] = None
    status: ConversationStatus
    is_cached: bool
    created_at: datetime
    updated_at: datetime
    last_accessed: Optional[datetime] = None
    url: Optional[str] = None
    redirect_url: Optional[str] = None
    message_count: int = 0


class ConversationStartRequest(BaseModel):
    """Request to start a new conversation"""
    init_prompt: str = Field(..., min_length=1, max_length=10000)
    mode: Optional[ChatGPTMode] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "init_prompt": "Help me understand quantum computing",
                "mode": "research"
            }
        }


class ConversationStartResponse(BaseResponse):
    """Response for conversation start"""
    conversation_id: str
    url: str
    redirect_url: Optional[str] = None


class ConversationResponse(BaseResponse):
    """Response for conversation query"""
    conversation: ConversationData
    messages: List[MessageData] = []
    cached: bool = False
    cache_timestamp: Optional[datetime] = None


class ConversationListResponse(BaseResponse):
    """Response for conversation list"""
    conversations: List[ConversationData]
    total: int
    page: int = 1
    page_size: int = 50


# Query Parameters
class ConversationQueryParams(BaseModel):
    """Query parameters for conversation list"""
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=50, ge=1, le=100)
    status: Optional[ConversationStatus] = None
    mode: Optional[ChatGPTMode] = None
    search: Optional[str] = None
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None


# Error Models
class ValidationError(BaseModel):
    """Validation error model"""
    loc: List[Union[str, int]]
    msg: str
    type: str


class HTTPValidationError(BaseModel):
    """HTTP validation error model"""
    detail: List[ValidationError]


# Export all models
__all__ = [
    # Enums
    "ChatGPTMode",
    "ConversationStatus", 
    "MessageRole",
    # Base models
    "BaseResponse",
    # Connection and health
    "ConnectionStatus",
    "SystemHealth",
    "HealthCheckResponse",
    "StatusResponse",
    # Messages
    "MessageData",
    "MessageRequest",
    "MessageResponse",
    # Conversations
    "ConversationData",
    "ConversationStartRequest",
    "ConversationStartResponse",
    "ConversationResponse",
    "ConversationListResponse",
    "ConversationQueryParams",
    # Errors
    "ValidationError",
    "HTTPValidationError",
]
