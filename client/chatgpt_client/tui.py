"""
Terminal User Interface (TUI) for ChatGPT Controller API Client

Rich-based TUI with Chinese language support and interactive navigation.
"""

import asyncio
import sys
from datetime import datetime
from typing import Optional, List, Dict, Any
from dataclasses import dataclass

from rich.console import Console
from rich.layout import Layout
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.align import Align
from rich.columns import Columns
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Prompt, Confirm
from rich.live import Live
from rich.tree import Tree
from rich import box

from .client import ChatGPTClient
from .models import ChatGPTMode, ConversationStatus
from .config import config_manager
from .exceptions import ChatGPTClientError
from .i18n import get_i18n, t, set_language, detect_language, Language


@dataclass
class AppState:
    """Application state"""
    current_tab: str = "status"
    conversations: List = None
    selected_conversation: Optional[str] = None
    status_data: Optional[Any] = None
    auto_refresh: bool = True
    language: Language = Language.EN


class TUIApp:
    """Main TUI Application"""
    
    def __init__(self):
        self.console = Console()
        self.state = AppState()
        self.client = None
        self.layout = self._create_layout()
        self.running = True
        
        # Detect and set language
        self.state.language = detect_language()
        set_language(self.state.language)
        
        # Initialize data
        self.state.conversations = []
    
    def _create_layout(self) -> Layout:
        """Create the main layout"""
        layout = Layout()
        
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main"),
            Layout(name="footer", size=3)
        )
        
        layout["main"].split_row(
            Layout(name="sidebar", size=20),
            Layout(name="content")
        )
        
        return layout
    
    def _create_header(self) -> Panel:
        """Create header panel"""
        title = Text("ChatGPT Controller Client", style="bold blue")
        subtitle = Text(f"Language: {self.state.language.value.upper()}", style="dim")
        
        header_content = Columns([
            Align.left(title),
            Align.right(subtitle)
        ])
        
        return Panel(
            header_content,
            title=t("main_menu"),
            border_style="blue",
            box=box.ROUNDED
        )
    
    def _create_sidebar(self) -> Panel:
        """Create sidebar with navigation"""
        tree = Tree(t("main_menu"))
        
        # Add navigation items
        status_style = "bold green" if self.state.current_tab == "status" else "white"
        conv_style = "bold green" if self.state.current_tab == "conversations" else "white"
        config_style = "bold green" if self.state.current_tab == "config" else "white"
        help_style = "bold green" if self.state.current_tab == "help" else "white"
        
        tree.add(f"[{status_style}]📊 {t('status_tab')}[/{status_style}]")
        tree.add(f"[{conv_style}]💬 {t('conversations_tab')}[/{conv_style}]")
        tree.add(f"[{config_style}]⚙️  {t('config_tab')}[/{config_style}]")
        tree.add(f"[{help_style}]❓ {t('help_tab')}[/{help_style}]")
        
        # Add controls
        tree.add("")
        controls = tree.add("🎮 Controls")
        controls.add("1-4: Switch tabs")
        controls.add("r: Refresh")
        controls.add("q: Quit")
        controls.add("l: Language")
        
        return Panel(
            tree,
            title=t("main_menu"),
            border_style="cyan",
            box=box.ROUNDED
        )
    
    def _create_footer(self) -> Panel:
        """Create footer with status"""
        status_text = ""
        if self.state.status_data:
            conn = self.state.status_data.connection_status
            if conn.connected:
                status_text = f"🟢 {t('connected')} ({conn.client_count} {t('clients')})"
            else:
                status_text = f"🔴 {t('disconnected')}"
        else:
            status_text = f"❓ {t('unknown')}"
        
        refresh_text = f"🔄 {t('auto_refresh') if self.state.auto_refresh else t('manual_refresh')}"
        
        footer_content = Columns([
            Align.left(Text(status_text)),
            Align.center(Text(f"Tab: {self.state.current_tab}")),
            Align.right(Text(refresh_text))
        ])
        
        return Panel(
            footer_content,
            border_style="dim",
            box=box.ROUNDED
        )
    
    def _create_status_content(self) -> Panel:
        """Create status tab content"""
        if not self.state.status_data:
            return Panel(
                Align.center(Text(t("loading"), style="yellow")),
                title=t("system_status"),
                border_style="yellow"
            )
        
        status = self.state.status_data
        
        # Create status table
        table = Table(show_header=True, header_style="bold magenta", box=box.ROUNDED)
        table.add_column(t("system_status"), style="cyan")
        table.add_column(t("status"), style="green")
        table.add_column(t("details"))
        
        # Connection status
        conn = status.connection_status
        conn_status = f"🟢 {t('connected')}" if conn.connected else f"🔴 {t('disconnected')}"
        conn_details = f"{t('clients')}: {conn.client_count}, {t('uptime')}: {conn.uptime_seconds:.1f}s"
        table.add_row("WebSocket", conn_status, conn_details)
        
        # System health
        health = status.system_health
        db_status = f"🟢 {t('healthy')}" if health.database_healthy else f"🔴 {t('unhealthy')}"
        ws_status = f"🟢 {t('healthy')}" if health.websocket_healthy else f"🔴 {t('unhealthy')}"
        
        table.add_row(t("database"), db_status, "")
        table.add_row(f"WebSocket {t('health_check')}", ws_status, "")
        table.add_row(
            t("system_metrics"),
            "📊 Monitoring",
            f"{t('memory')}: {health.memory_usage_mb:.1f}MB, {t('cpu')}: {health.cpu_usage_percent:.1f}%"
        )
        
        return Panel(
            table,
            title=t("system_status"),
            border_style="green" if status.success else "red"
        )
    
    def _create_conversations_content(self) -> Panel:
        """Create conversations tab content"""
        if not self.state.conversations:
            return Panel(
                Align.center(Text(t("no_conversations_found"), style="yellow")),
                title=t("conversations"),
                border_style="yellow"
            )
        
        # Create conversations table
        table = Table(show_header=True, header_style="bold magenta", box=box.ROUNDED)
        table.add_column("ID", style="cyan", width=20)
        table.add_column(t("conversation_title"), style="white", width=25)
        table.add_column(t("conversation_mode"), style="yellow", width=10)
        table.add_column(t("conversation_status"), style="green", width=10)
        table.add_column(t("message_count"), style="blue", width=8)
        table.add_column(t("created_at"), style="dim", width=16)
        
        for conv in self.state.conversations[:10]:  # Show first 10
            # Truncate title if too long
            title = conv.title or t("conversation")
            if len(title) > 22:
                title = title[:19] + "..."
            
            # Format status with emoji
            status_emoji = {
                "active": "🟢",
                "archived": "📁",
                "deleted": "🗑️"
            }
            status_display = f"{status_emoji.get(conv.status, '❓')} {t(f'status_{conv.status}')}"
            
            # Highlight selected conversation
            style = "bold" if conv.id == self.state.selected_conversation else None
            
            table.add_row(
                conv.id[:18] + "..." if len(conv.id) > 18 else conv.id,
                title,
                t(f"mode_{conv.mode}") if conv.mode else t("mode_none"),
                status_display,
                str(conv.message_count),
                conv.created_at.strftime("%Y-%m-%d %H:%M")[:16],
                style=style
            )
        
        return Panel(
            table,
            title=f"{t('conversations')} ({len(self.state.conversations)})",
            border_style="blue"
        )
    
    def _create_config_content(self) -> Panel:
        """Create configuration tab content"""
        config = config_manager.config
        
        # Create config table
        table = Table(show_header=True, header_style="bold magenta", box=box.ROUNDED)
        table.add_column(t("configuration"), style="cyan")
        table.add_column(t("value"), style="white")
        
        table.add_row(t("base_url"), config.base_url)
        table.add_row(t("api_version"), config.api_version)
        table.add_row(t("timeout"), f"{config.timeout}s")
        table.add_row(t("output_format"), config.cli_output_format)
        table.add_row(t("color_output"), t("yes") if config.cli_color else t("no"))
        table.add_row(t("config_file"), str(config_manager.config_file))
        
        return Panel(
            table,
            title=t("configuration"),
            border_style="yellow"
        )
    
    def _create_help_content(self) -> Panel:
        """Create help tab content"""
        help_text = f"""
[bold]{t('help')}[/bold]

[cyan]Keyboard Shortcuts:[/cyan]
• 1-4: {t('conversations_tab')} tabs
• r: {t('refresh')}
• q: {t('exit')}
• l: Switch language (EN/中文)

[cyan]Navigation:[/cyan]
• Use number keys to switch between tabs
• Press 'r' to refresh current data
• Press 'q' to quit the application

[cyan]Features:[/cyan]
• Real-time system status monitoring
• Conversation management and browsing
• Configuration viewing
• Multi-language support (English/Chinese)

[cyan]API Endpoints:[/cyan]
• System status and health checks
• Conversation listing and details
• Message sending and receiving
• Real-time WebSocket monitoring
        """
        
        return Panel(
            help_text,
            title=t("help"),
            border_style="magenta"
        )
    
    def _get_content_panel(self) -> Panel:
        """Get content panel based on current tab"""
        if self.state.current_tab == "status":
            return self._create_status_content()
        elif self.state.current_tab == "conversations":
            return self._create_conversations_content()
        elif self.state.current_tab == "config":
            return self._create_config_content()
        elif self.state.current_tab == "help":
            return self._create_help_content()
        else:
            return Panel("Unknown tab", border_style="red")
    
    def _update_layout(self):
        """Update the layout with current content"""
        self.layout["header"].update(self._create_header())
        self.layout["sidebar"].update(self._create_sidebar())
        self.layout["content"].update(self._get_content_panel())
        self.layout["footer"].update(self._create_footer())
    
    async def _refresh_status(self):
        """Refresh system status"""
        try:
            if not self.client:
                self.client = ChatGPTClient()
            
            self.state.status_data = await self.client.get_status()
        except ChatGPTClientError:
            self.state.status_data = None
    
    async def _refresh_conversations(self):
        """Refresh conversations list"""
        try:
            if not self.client:
                self.client = ChatGPTClient()
            
            result = await self.client.list_conversations(page_size=50)
            self.state.conversations = result.conversations
        except ChatGPTClientError:
            self.state.conversations = []
    
    async def _refresh_current_tab(self):
        """Refresh data for current tab"""
        if self.state.current_tab == "status":
            await self._refresh_status()
        elif self.state.current_tab == "conversations":
            await self._refresh_conversations()
    
    def _handle_key(self, key: str):
        """Handle keyboard input"""
        if key == "q":
            self.running = False
        elif key == "r":
            # Refresh will be handled in main loop
            pass
        elif key == "l":
            # Toggle language
            new_lang = Language.ZH if self.state.language == Language.EN else Language.EN
            self.state.language = new_lang
            set_language(new_lang)
        elif key in "1234":
            # Switch tabs
            tabs = ["status", "conversations", "config", "help"]
            tab_index = int(key) - 1
            if 0 <= tab_index < len(tabs):
                self.state.current_tab = tabs[tab_index]
    
    async def run(self):
        """Run the TUI application"""
        self.console.print(f"🚀 {t('loading')} ChatGPT Controller TUI...")
        
        # Initial data load
        await self._refresh_status()
        await self._refresh_conversations()
        
        with Live(self.layout, console=self.console, refresh_per_second=2) as live:
            try:
                import keyboard
                keyboard_available = True
            except ImportError:
                keyboard_available = False
                self.console.print("⚠️  Keyboard module not available. Use Ctrl+C to exit.")
            
            last_refresh = datetime.now()
            
            while self.running:
                try:
                    # Update layout
                    self._update_layout()
                    
                    # Handle keyboard input if available
                    if keyboard_available:
                        try:
                            if keyboard.is_pressed('q'):
                                break
                            elif keyboard.is_pressed('r'):
                                await self._refresh_current_tab()
                                last_refresh = datetime.now()
                            elif keyboard.is_pressed('l'):
                                self._handle_key('l')
                            elif keyboard.is_pressed('1'):
                                self._handle_key('1')
                            elif keyboard.is_pressed('2'):
                                self._handle_key('2')
                            elif keyboard.is_pressed('3'):
                                self._handle_key('3')
                            elif keyboard.is_pressed('4'):
                                self._handle_key('4')
                        except:
                            pass
                    
                    # Auto refresh every 30 seconds
                    if self.state.auto_refresh:
                        now = datetime.now()
                        if (now - last_refresh).seconds >= 30:
                            await self._refresh_current_tab()
                            last_refresh = now
                    
                    await asyncio.sleep(0.5)
                    
                except KeyboardInterrupt:
                    break
        
        if self.client:
            await self.client.close()
        
        self.console.print(f"👋 {t('goodbye')}")


def main():
    """Main entry point for TUI"""
    app = TUIApp()
    
    try:
        asyncio.run(app.run())
    except KeyboardInterrupt:
        print(f"\n👋 {t('goodbye')}")
    except Exception as e:
        print(f"💥 {t('unexpected_error')}: {e}")


if __name__ == "__main__":
    main()
