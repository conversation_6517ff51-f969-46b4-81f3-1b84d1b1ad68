"""
Command Line Interface for ChatGPT Controller API Client

Rich-based CLI with commands for all API operations.
"""

import asyncio
from datetime import datetime
from typing import Optional, List
from pathlib import Path

import typer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Prompt, Confirm
from rich.syntax import Syntax
from rich.json import <PERSON><PERSON><PERSON>
from rich import print as rprint

from .client import ChatGPTClient
from .models import ChatGPTMode, ConversationStatus
from .config import config_manager
from .exceptions import ChatGPTClientError
from .i18n import get_i18n, t, set_language, detect_language, Language


# Create Typer app and Rich console
app = typer.Typer(
    name="chatgpt-cli",
    help="ChatGPT Controller API Client CLI",
    add_completion=False
)
console = Console()

# Initialize language support
set_language(detect_language())


def handle_async(coro):
    """Helper to run async functions in CLI commands"""
    return asyncio.run(coro)


def format_datetime(dt: Optional[datetime]) -> str:
    """Format datetime for display"""
    if dt is None:
        return "N/A"
    return dt.strftime("%Y-%m-%d %H:%M:%S")


def create_status_table(status_data) -> Table:
    """Create a table for status information"""
    table = Table(title="System Status", show_header=True, header_style="bold magenta")
    table.add_column("Component", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Details")
    
    # Connection status
    conn = status_data.connection_status
    conn_status = "🟢 Connected" if conn.connected else "🔴 Disconnected"
    conn_details = f"Clients: {conn.client_count}, Uptime: {conn.uptime_seconds:.1f}s"
    table.add_row("WebSocket", conn_status, conn_details)
    
    # System health
    health = status_data.system_health
    db_status = "🟢 Healthy" if health.database_healthy else "🔴 Unhealthy"
    ws_status = "🟢 Healthy" if health.websocket_healthy else "🔴 Unhealthy"
    
    table.add_row("Database", db_status, "")
    table.add_row("WebSocket Health", ws_status, "")
    table.add_row(
        "System Resources", 
        "📊 Monitoring",
        f"Memory: {health.memory_usage_mb:.1f}MB, CPU: {health.cpu_usage_percent:.1f}%"
    )
    
    return table


def create_conversations_table(conversations: List) -> Table:
    """Create a table for conversations list"""
    table = Table(title="Conversations", show_header=True, header_style="bold magenta")
    table.add_column("ID", style="cyan", width=20)
    table.add_column("Title", style="white", width=30)
    table.add_column("Mode", style="yellow", width=12)
    table.add_column("Status", style="green", width=10)
    table.add_column("Messages", style="blue", width=8)
    table.add_column("Created", style="dim", width=16)
    
    for conv in conversations:
        # Truncate title if too long
        title = conv.title or "Untitled"
        if len(title) > 27:
            title = title[:24] + "..."
        
        # Format status with emoji
        status_emoji = {
            "active": "🟢",
            "archived": "📁", 
            "deleted": "🗑️"
        }
        status_display = f"{status_emoji.get(conv.status, '❓')} {conv.status}"
        
        table.add_row(
            conv.id[:18] + "..." if len(conv.id) > 18 else conv.id,
            title,
            conv.mode or "N/A",
            status_display,
            str(conv.message_count),
            format_datetime(conv.created_at)[:16]
        )
    
    return table


# Status Commands
@app.command()
def status(
    json_output: bool = typer.Option(False, "--json", help="Output as JSON")
):
    """Get system status and health information"""
    
    async def _get_status():
        async with ChatGPTClient() as client:
            try:
                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    console=console
                ) as progress:
                    task = progress.add_task("Getting system status...", total=None)
                    status_data = await client.get_status()
                    progress.update(task, completed=True)
                
                if json_output:
                    console.print(JSON(status_data.model_dump_json()))
                else:
                    table = create_status_table(status_data)
                    console.print(table)
                    
                    # Show success message
                    if status_data.success:
                        console.print("✅ [green]System is operational[/green]")
                    else:
                        console.print("❌ [red]System has issues[/red]")
                        
            except ChatGPTClientError as e:
                console.print(f"❌ [red]Error: {e.message}[/red]")
                raise typer.Exit(1)
    
    handle_async(_get_status())


@app.command()
def health():
    """Quick health check"""
    
    async def _health_check():
        async with ChatGPTClient() as client:
            try:
                health_data = await client.health_check()
                
                # Show overall status
                if health_data.status == "healthy":
                    console.print("✅ [green]System is healthy[/green]")
                else:
                    console.print("❌ [red]System is unhealthy[/red]")
                
                # Show individual checks
                console.print("\n[bold]Component Status:[/bold]")
                for component, status in health_data.checks.items():
                    status_icon = "✅" if status else "❌"
                    status_color = "green" if status else "red"
                    console.print(f"  {status_icon} {component}: [{status_color}]{status}[/{status_color}]")
                
                console.print(f"\n[dim]Uptime: {health_data.uptime_seconds:.1f} seconds[/dim]")
                
            except ChatGPTClientError as e:
                console.print(f"❌ [red]Error: {e.message}[/red]")
                raise typer.Exit(1)
    
    handle_async(_health_check())


@app.command()
def metrics(
    json_output: bool = typer.Option(False, "--json", help="Output as JSON")
):
    """Get detailed system metrics"""
    
    async def _get_metrics():
        async with ChatGPTClient() as client:
            try:
                metrics_data = await client.get_metrics()
                
                if json_output:
                    console.print(JSON.from_data(metrics_data))
                else:
                    console.print(Panel(
                        JSON.from_data(metrics_data),
                        title="System Metrics",
                        border_style="blue"
                    ))
                    
            except ChatGPTClientError as e:
                console.print(f"❌ [red]Error: {e.message}[/red]")
                raise typer.Exit(1)
    
    handle_async(_get_metrics())


# Conversation Commands
@app.command()
def start(
    prompt: str = typer.Argument(..., help="Initial prompt for the conversation"),
    mode: Optional[str] = typer.Option(None, help="Conversation mode (research, reason, search, canvas, picture_v2)"),
    interactive: bool = typer.Option(False, "--interactive", "-i", help="Start interactive mode after creation")
):
    """Start a new ChatGPT conversation"""
    
    async def _start_conversation():
        async with ChatGPTClient() as client:
            try:
                # Validate mode if provided
                chat_mode = None
                if mode:
                    try:
                        chat_mode = ChatGPTMode(mode)
                    except ValueError:
                        console.print(f"❌ [red]Invalid mode: {mode}[/red]")
                        console.print(f"Valid modes: {', '.join([m.value for m in ChatGPTMode])}")
                        raise typer.Exit(1)
                
                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    console=console
                ) as progress:
                    task = progress.add_task("Starting conversation...", total=None)
                    result = await client.start_conversation(prompt, chat_mode)
                    progress.update(task, completed=True)
                
                console.print("✅ [green]Conversation started successfully![/green]")
                console.print(f"🆔 Conversation ID: [cyan]{result.conversation_id}[/cyan]")
                console.print(f"🔗 URL: [blue]{result.url}[/blue]")
                
                if interactive:
                    await _interactive_chat(client, result.conversation_id)
                    
            except ChatGPTClientError as e:
                console.print(f"❌ [red]Error: {e.message}[/red]")
                raise typer.Exit(1)
    
    handle_async(_start_conversation())


async def _interactive_chat(client: ChatGPTClient, conversation_id: str):
    """Interactive chat mode"""
    console.print("\n[bold green]🚀 Interactive Chat Mode[/bold green]")
    console.print("[dim]Type 'exit' or 'quit' to leave, 'help' for commands[/dim]\n")
    
    while True:
        try:
            message = Prompt.ask("[bold blue]You[/bold blue]")
            
            if message.lower() in ['exit', 'quit']:
                console.print("👋 [yellow]Goodbye![/yellow]")
                break
            elif message.lower() == 'help':
                console.print("""
[bold]Available commands:[/bold]
  exit, quit - Exit interactive mode
  help       - Show this help message
  
Just type your message to send it to ChatGPT!
                """)
                continue
            elif not message.strip():
                continue
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task("Sending message...", total=None)
                await client.send_message(conversation_id, message)
                progress.update(task, completed=True)
            
            console.print("✅ [green]Message sent![/green]")
            console.print("[dim]Check the browser for ChatGPT's response[/dim]\n")
            
        except KeyboardInterrupt:
            console.print("\n👋 [yellow]Goodbye![/yellow]")
            break
        except ChatGPTClientError as e:
            console.print(f"❌ [red]Error: {e.message}[/red]")


@app.command()
def list(
    page: int = typer.Option(1, help="Page number"),
    page_size: int = typer.Option(20, help="Number of conversations per page"),
    status: Optional[str] = typer.Option(None, help="Filter by status (active, archived, deleted)"),
    mode: Optional[str] = typer.Option(None, help="Filter by mode"),
    search: Optional[str] = typer.Option(None, help="Search in titles and prompts"),
    json_output: bool = typer.Option(False, "--json", help="Output as JSON")
):
    """List conversations with filtering options"""
    
    async def _list_conversations():
        async with ChatGPTClient() as client:
            try:
                # Validate status and mode if provided
                conv_status = None
                if status:
                    try:
                        conv_status = ConversationStatus(status)
                    except ValueError:
                        console.print(f"❌ [red]Invalid status: {status}[/red]")
                        raise typer.Exit(1)
                
                conv_mode = None
                if mode:
                    try:
                        conv_mode = ChatGPTMode(mode)
                    except ValueError:
                        console.print(f"❌ [red]Invalid mode: {mode}[/red]")
                        raise typer.Exit(1)
                
                result = await client.list_conversations(
                    page=page,
                    page_size=page_size,
                    status=conv_status,
                    mode=conv_mode,
                    search=search
                )
                
                if json_output:
                    console.print(JSON(result.model_dump_json()))
                else:
                    if not result.conversations:
                        console.print("📭 [yellow]No conversations found[/yellow]")
                        return
                    
                    table = create_conversations_table(result.conversations)
                    console.print(table)
                    
                    # Show pagination info
                    total_pages = (result.total + page_size - 1) // page_size
                    console.print(f"\n[dim]Page {page} of {total_pages} | Total: {result.total} conversations[/dim]")
                    
            except ChatGPTClientError as e:
                console.print(f"❌ [red]Error: {e.message}[/red]")
                raise typer.Exit(1)
    
    handle_async(_list_conversations())


@app.command()
def get(
    conversation_id: str = typer.Argument(..., help="Conversation ID"),
    use_cache: bool = typer.Option(True, help="Use cached data if available"),
    show_messages: bool = typer.Option(True, help="Show conversation messages"),
    json_output: bool = typer.Option(False, "--json", help="Output as JSON")
):
    """Get conversation details and messages"""

    async def _get_conversation():
        async with ChatGPTClient() as client:
            try:
                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    console=console
                ) as progress:
                    task = progress.add_task("Getting conversation...", total=None)
                    result = await client.get_conversation(conversation_id, use_cache)
                    progress.update(task, completed=True)

                if json_output:
                    console.print(JSON(result.model_dump_json()))
                    return

                # Show conversation info
                conv = result.conversation
                console.print(Panel(
                    f"""[bold]ID:[/bold] {conv.id}
[bold]Title:[/bold] {conv.title or 'Untitled'}
[bold]Mode:[/bold] {conv.mode or 'N/A'}
[bold]Status:[/bold] {conv.status}
[bold]Created:[/bold] {format_datetime(conv.created_at)}
[bold]Updated:[/bold] {format_datetime(conv.updated_at)}
[bold]Messages:[/bold] {conv.message_count}
[bold]Cached:[/bold] {'Yes' if conv.is_cached else 'No'}""",
                    title="Conversation Details",
                    border_style="cyan"
                ))

                if conv.init_prompt:
                    console.print(Panel(
                        conv.init_prompt,
                        title="Initial Prompt",
                        border_style="green"
                    ))

                # Show messages if requested
                if show_messages and result.messages:
                    console.print("\n[bold]Messages:[/bold]")
                    for i, msg in enumerate(result.messages, 1):
                        role_color = "blue" if msg.role == "user" else "green"
                        role_emoji = "👤" if msg.role == "user" else "🤖"

                        console.print(Panel(
                            msg.content,
                            title=f"{role_emoji} {msg.role.title()} - {format_datetime(msg.created_at)}",
                            border_style=role_color
                        ))

            except ChatGPTClientError as e:
                console.print(f"❌ [red]Error: {e.message}[/red]")
                raise typer.Exit(1)

    handle_async(_get_conversation())


@app.command()
def send(
    conversation_id: str = typer.Argument(..., help="Conversation ID"),
    message: Optional[str] = typer.Argument(None, help="Message to send"),
    interactive: bool = typer.Option(False, "--interactive", "-i", help="Enter interactive mode after sending")
):
    """Send a message to a conversation"""

    async def _send_message():
        async with ChatGPTClient() as client:
            try:
                # Get message from user if not provided
                if not message:
                    msg_text = Prompt.ask("[bold blue]Enter your message[/bold blue]")
                else:
                    msg_text = message

                if not msg_text.strip():
                    console.print("❌ [red]Message cannot be empty[/red]")
                    raise typer.Exit(1)

                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    console=console
                ) as progress:
                    task = progress.add_task("Sending message...", total=None)
                    result = await client.send_message(conversation_id, msg_text)
                    progress.update(task, completed=True)

                console.print("✅ [green]Message sent successfully![/green]")
                console.print(f"🆔 Message ID: [cyan]{result.message_id}[/cyan]")
                console.print(f"⏰ Sent at: [dim]{format_datetime(result.sent_at)}[/dim]")

                if interactive:
                    await _interactive_chat(client, conversation_id)

            except ChatGPTClientError as e:
                console.print(f"❌ [red]Error: {e.message}[/red]")
                raise typer.Exit(1)

    handle_async(_send_message())


# Configuration Commands
@app.command()
def config(
    show: bool = typer.Option(False, "--show", help="Show current configuration"),
    set_url: Optional[str] = typer.Option(None, "--url", help="Set API base URL"),
    set_timeout: Optional[int] = typer.Option(None, "--timeout", help="Set request timeout"),
    reset: bool = typer.Option(False, "--reset", help="Reset to default configuration")
):
    """Manage client configuration"""

    if reset:
        if Confirm.ask("Are you sure you want to reset configuration to defaults?"):
            config_manager.config_file.unlink(missing_ok=True)
            config_manager._config = None
            console.print("✅ [green]Configuration reset to defaults[/green]")
        return

    if set_url or set_timeout:
        updates = {}
        if set_url:
            updates['base_url'] = set_url
        if set_timeout:
            updates['timeout'] = set_timeout

        config_manager.update_config(**updates)
        console.print("✅ [green]Configuration updated[/green]")

    if show or not (set_url or set_timeout or reset):
        config = config_manager.config
        console.print(Panel(
            f"""[bold]API Configuration:[/bold]
Base URL: {config.base_url}
API Version: {config.api_version}
Timeout: {config.timeout}s

[bold]CLI Configuration:[/bold]
Output Format: {config.cli_output_format}
Color Output: {config.cli_color}

[bold]Files:[/bold]
Config File: {config_manager.config_file}""",
            title="Client Configuration",
            border_style="yellow"
        ))


@app.command()
def language(
    lang: Optional[str] = typer.Argument(None, help="Language code (en/zh)")
):
    """Set or show current language"""
    current_lang = get_i18n().language

    if not lang:
        console.print(f"Current language: {current_lang.value}")
        console.print("Available languages: en (English), zh (中文)")
        return

    if lang.lower() == "en":
        set_language(Language.EN)
        console.print("Language set to English")
    elif lang.lower() == "zh":
        set_language(Language.ZH)
        console.print("语言已设置为中文")
    else:
        console.print(f"❌ [red]Unsupported language: {lang}[/red]")
        console.print("Available languages: en (English), zh (中文)")
        raise typer.Exit(1)


if __name__ == "__main__":
    app()
