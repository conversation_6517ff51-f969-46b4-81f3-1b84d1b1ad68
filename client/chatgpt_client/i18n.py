"""
Internationalization support for ChatGPT Controller Client

Provides multi-language support with Chinese and English translations.
"""

import os
from typing import Dict, Any
from enum import Enum


class Language(Enum):
    """Supported languages"""
    EN = "en"
    ZH = "zh"


class I18n:
    """Internationalization manager"""
    
    def __init__(self, language: Language = Language.EN):
        self.language = language
        self._translations = self._load_translations()
    
    def _load_translations(self) -> Dict[str, Dict[str, str]]:
        """Load translation dictionaries"""
        return {
            "en": {
                # Common
                "yes": "Yes",
                "no": "No",
                "ok": "OK",
                "cancel": "Cancel",
                "error": "Error",
                "success": "Success",
                "loading": "Loading",
                "connecting": "Connecting",
                "connected": "Connected",
                "disconnected": "Disconnected",
                "healthy": "Healthy",
                "unhealthy": "Unhealthy",
                "unknown": "Unknown",
                "refresh": "Refresh",
                "exit": "Exit",
                "help": "Help",
                "about": "About",
                "configuration": "Configuration",
                "save": "Save",
                "reset": "Reset",
                
                # Status
                "system_status": "System Status",
                "connection_status": "Connection Status",
                "database": "Database",
                "websocket": "WebSocket",
                "uptime": "Uptime",
                "memory": "Memory",
                "cpu": "CPU",
                "clients": "Clients",
                "health_check": "Health Check",
                "system_metrics": "System Metrics",
                
                # Conversations
                "conversations": "Conversations",
                "conversation": "Conversation",
                "start_conversation": "Start Conversation",
                "new_conversation": "New Conversation",
                "conversation_details": "Conversation Details",
                "conversation_id": "Conversation ID",
                "conversation_title": "Title",
                "conversation_mode": "Mode",
                "conversation_status": "Status",
                "message_count": "Messages",
                "created_at": "Created",
                "updated_at": "Updated",
                "initial_prompt": "Initial Prompt",
                "send_message": "Send Message",
                "message_sent": "Message sent successfully",
                "conversation_started": "Conversation started successfully",
                
                # Messages
                "messages": "Messages",
                "message": "Message",
                "user": "User",
                "assistant": "Assistant",
                "system": "System",
                "enter_message": "Enter your message",
                "enter_prompt": "Enter initial prompt",
                "message_placeholder": "Type your message here...",
                "prompt_placeholder": "Enter your initial prompt...",
                
                # Modes
                "mode_research": "Research",
                "mode_reason": "Reason",
                "mode_search": "Search",
                "mode_canvas": "Canvas",
                "mode_picture_v2": "Picture V2",
                "mode_none": "None",
                
                # Status values
                "status_active": "Active",
                "status_archived": "Archived",
                "status_deleted": "Deleted",
                
                # CLI/TUI
                "interactive_mode": "Interactive Mode",
                "type_exit_to_quit": "Type 'exit' or 'quit' to leave",
                "type_help_for_commands": "Type 'help' for commands",
                "goodbye": "Goodbye!",
                "page": "Page",
                "total": "Total",
                "no_conversations_found": "No conversations found",
                "system_operational": "System is operational",
                "system_has_issues": "System has issues",
                "cannot_connect": "Cannot connect to API server",
                "invalid_mode": "Invalid mode",
                "invalid_status": "Invalid status",
                "conversation_not_found": "Conversation not found",
                "message_cannot_be_empty": "Message cannot be empty",
                
                # Configuration
                "base_url": "Base URL",
                "api_version": "API Version",
                "timeout": "Timeout",
                "output_format": "Output Format",
                "color_output": "Color Output",
                "config_file": "Config File",
                "config_updated": "Configuration updated",
                "config_reset": "Configuration reset to defaults",
                "confirm_reset": "Are you sure you want to reset configuration to defaults?",
                
                # TUI specific
                "main_menu": "Main Menu",
                "status_tab": "Status",
                "conversations_tab": "Conversations",
                "config_tab": "Configuration",
                "help_tab": "Help",
                "quit_app": "Quit Application",
                "refresh_all": "Refresh All",
                "auto_refresh": "Auto Refresh",
                "manual_refresh": "Manual Refresh",
                "filter_conversations": "Filter Conversations",
                "search_conversations": "Search Conversations",
                "view_details": "View Details",
                "send_new_message": "Send New Message",
                
                # Errors
                "api_error": "API Error",
                "connection_error": "Connection Error",
                "validation_error": "Validation Error",
                "server_error": "Server Error",
                "not_found_error": "Not Found",
                "timeout_error": "Timeout Error",
                "unexpected_error": "Unexpected Error",
            },
            
            "zh": {
                # Common
                "yes": "是",
                "no": "否",
                "ok": "确定",
                "cancel": "取消",
                "error": "错误",
                "success": "成功",
                "loading": "加载中",
                "connecting": "连接中",
                "connected": "已连接",
                "disconnected": "已断开",
                "healthy": "健康",
                "unhealthy": "不健康",
                "unknown": "未知",
                "refresh": "刷新",
                "exit": "退出",
                "help": "帮助",
                "about": "关于",
                "configuration": "配置",
                "save": "保存",
                "reset": "重置",
                
                # Status
                "system_status": "系统状态",
                "connection_status": "连接状态",
                "database": "数据库",
                "websocket": "WebSocket",
                "uptime": "运行时间",
                "memory": "内存",
                "cpu": "CPU",
                "clients": "客户端",
                "health_check": "健康检查",
                "system_metrics": "系统指标",
                
                # Conversations
                "conversations": "对话",
                "conversation": "对话",
                "start_conversation": "开始对话",
                "new_conversation": "新对话",
                "conversation_details": "对话详情",
                "conversation_id": "对话ID",
                "conversation_title": "标题",
                "conversation_mode": "模式",
                "conversation_status": "状态",
                "message_count": "消息数",
                "created_at": "创建时间",
                "updated_at": "更新时间",
                "initial_prompt": "初始提示",
                "send_message": "发送消息",
                "message_sent": "消息发送成功",
                "conversation_started": "对话创建成功",
                
                # Messages
                "messages": "消息",
                "message": "消息",
                "user": "用户",
                "assistant": "助手",
                "system": "系统",
                "enter_message": "输入您的消息",
                "enter_prompt": "输入初始提示",
                "message_placeholder": "在此输入您的消息...",
                "prompt_placeholder": "输入您的初始提示...",
                
                # Modes
                "mode_research": "研究",
                "mode_reason": "推理",
                "mode_search": "搜索",
                "mode_canvas": "画布",
                "mode_picture_v2": "图片V2",
                "mode_none": "无",
                
                # Status values
                "status_active": "活跃",
                "status_archived": "已归档",
                "status_deleted": "已删除",
                
                # CLI/TUI
                "interactive_mode": "交互模式",
                "type_exit_to_quit": "输入 'exit' 或 'quit' 退出",
                "type_help_for_commands": "输入 'help' 查看命令",
                "goodbye": "再见！",
                "page": "页",
                "total": "总计",
                "no_conversations_found": "未找到对话",
                "system_operational": "系统运行正常",
                "system_has_issues": "系统存在问题",
                "cannot_connect": "无法连接到API服务器",
                "invalid_mode": "无效模式",
                "invalid_status": "无效状态",
                "conversation_not_found": "未找到对话",
                "message_cannot_be_empty": "消息不能为空",
                
                # Configuration
                "base_url": "基础URL",
                "api_version": "API版本",
                "timeout": "超时时间",
                "output_format": "输出格式",
                "color_output": "彩色输出",
                "config_file": "配置文件",
                "config_updated": "配置已更新",
                "config_reset": "配置已重置为默认值",
                "confirm_reset": "确定要重置配置为默认值吗？",
                
                # TUI specific
                "main_menu": "主菜单",
                "status_tab": "状态",
                "conversations_tab": "对话",
                "config_tab": "配置",
                "help_tab": "帮助",
                "quit_app": "退出应用",
                "refresh_all": "刷新全部",
                "auto_refresh": "自动刷新",
                "manual_refresh": "手动刷新",
                "filter_conversations": "筛选对话",
                "search_conversations": "搜索对话",
                "view_details": "查看详情",
                "send_new_message": "发送新消息",
                
                # Errors
                "api_error": "API错误",
                "connection_error": "连接错误",
                "validation_error": "验证错误",
                "server_error": "服务器错误",
                "not_found_error": "未找到",
                "timeout_error": "超时错误",
                "unexpected_error": "意外错误",
            }
        }
    
    def t(self, key: str, **kwargs) -> str:
        """Translate a key to the current language"""
        translations = self._translations.get(self.language.value, self._translations["en"])
        text = translations.get(key, key)
        
        # Simple string formatting
        if kwargs:
            try:
                text = text.format(**kwargs)
            except (KeyError, ValueError):
                pass
        
        return text
    
    def set_language(self, language: Language):
        """Set the current language"""
        self.language = language


# Global instance
_i18n = I18n()

def get_i18n() -> I18n:
    """Get the global i18n instance"""
    return _i18n

def t(key: str, **kwargs) -> str:
    """Shortcut for translation"""
    return _i18n.t(key, **kwargs)

def set_language(language: Language):
    """Set global language"""
    _i18n.set_language(language)

def detect_language() -> Language:
    """Detect system language"""
    import locale
    
    try:
        # Get system locale
        lang_code = locale.getdefaultlocale()[0]
        if lang_code and lang_code.startswith('zh'):
            return Language.ZH
    except:
        pass
    
    # Check environment variables
    for env_var in ['LANG', 'LANGUAGE', 'LC_ALL', 'LC_MESSAGES']:
        lang = os.environ.get(env_var, '')
        if lang.startswith('zh'):
            return Language.ZH
    
    return Language.EN
