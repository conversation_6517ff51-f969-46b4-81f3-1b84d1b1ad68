"""
ChatGPT Controller API Client

Main client class for interacting with the ChatGPT Controller API.
"""

import asyncio
from typing import Optional, Dict, Any, List
from datetime import datetime

import httpx
from pydantic import ValidationError

from .models import (
    StatusResponse,
    HealthCheckResponse,
    ConversationStartRequest,
    ConversationStartResponse,
    ConversationResponse,
    ConversationListResponse,
    ConversationQueryParams,
    MessageRequest,
    MessageResponse,
    ChatGPTMode,
    ConversationStatus,
)
from .config import ClientConfig, config_manager
from .exceptions import (
    ChatGPTClientError,
    APIError,
    ValidationError as ClientValidationError,
    ConnectionError as ClientConnectionError,
)


class ChatGPTClient:
    """
    Main client for ChatGPT Controller API
    
    Provides methods for all API endpoints including status monitoring,
    conversation management, and message sending.
    """
    
    def __init__(
        self,
        base_url: Optional[str] = None,
        timeout: Optional[int] = None,
        config: Optional[ClientConfig] = None
    ):
        """
        Initialize the ChatGPT Controller API client
        
        Args:
            base_url: API base URL (overrides config)
            timeout: Request timeout in seconds (overrides config)
            config: Client configuration (uses global config if None)
        """
        self.config = config or config_manager.config
        self.base_url = base_url or self.config.api_base_url
        self.timeout = timeout or self.config.timeout
        
        # Create HTTP client
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            timeout=self.timeout,
            headers={
                "Content-Type": "application/json",
                "User-Agent": f"chatgpt-controller-client/0.1.0",
            }
        )
    
    async def __aenter__(self):
        """Async context manager entry"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        response_model: Optional[type] = None
    ) -> Any:
        """
        Make an HTTP request to the API
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint path
            params: Query parameters
            json_data: JSON request body
            response_model: Pydantic model to parse response
            
        Returns:
            Parsed response data or raw response
            
        Raises:
            ChatGPTClientError: For various client errors
        """
        try:
            response = await self.client.request(
                method=method,
                url=endpoint,
                params=params,
                json=json_data
            )
            
            # Check for HTTP errors
            if response.status_code >= 400:
                error_detail = "Unknown error"
                try:
                    error_data = response.json()
                    error_detail = error_data.get("detail", str(error_data))
                except:
                    error_detail = response.text or f"HTTP {response.status_code}"
                
                if response.status_code == 404:
                    raise APIError(f"Not found: {error_detail}", status_code=404)
                elif response.status_code == 422:
                    raise ClientValidationError(f"Validation error: {error_detail}")
                elif response.status_code >= 500:
                    raise APIError(f"Server error: {error_detail}", status_code=response.status_code)
                else:
                    raise APIError(f"API error: {error_detail}", status_code=response.status_code)
            
            # Parse response
            response_data = response.json()
            
            if response_model:
                try:
                    return response_model(**response_data)
                except ValidationError as e:
                    raise ClientValidationError(f"Response validation error: {e}")
            
            return response_data
            
        except httpx.ConnectError as e:
            raise ClientConnectionError(f"Failed to connect to API: {e}")
        except httpx.TimeoutException as e:
            raise ClientConnectionError(f"Request timeout: {e}")
        except httpx.HTTPError as e:
            raise ChatGPTClientError(f"HTTP error: {e}")
    
    # Status and Health Endpoints
    
    async def get_status(self) -> StatusResponse:
        """
        Get system status including WebSocket connection and health metrics
        
        Returns:
            StatusResponse: System status information
        """
        return await self._make_request(
            "GET", "/status", response_model=StatusResponse
        )
    
    async def health_check(self) -> HealthCheckResponse:
        """
        Simple health check endpoint for monitoring
        
        Returns:
            HealthCheckResponse: Basic health status
        """
        return await self._make_request(
            "GET", "/health", response_model=HealthCheckResponse
        )
    
    async def get_metrics(self) -> Dict[str, Any]:
        """
        Get detailed system metrics
        
        Returns:
            Dict: Detailed system metrics
        """
        return await self._make_request("GET", "/metrics")
    
    # Conversation Management Endpoints
    
    async def start_conversation(
        self,
        init_prompt: str,
        mode: Optional[ChatGPTMode] = None
    ) -> ConversationStartResponse:
        """
        Start a new ChatGPT conversation
        
        Args:
            init_prompt: Initial prompt for the conversation
            mode: ChatGPT conversation mode
            
        Returns:
            ConversationStartResponse: New conversation details
        """
        request_data = ConversationStartRequest(
            init_prompt=init_prompt,
            mode=mode
        )
        
        return await self._make_request(
            "POST",
            "/conversations/start",
            json_data=request_data.model_dump(exclude_none=True),
            response_model=ConversationStartResponse
        )
    
    async def list_conversations(
        self,
        page: int = 1,
        page_size: int = 50,
        status: Optional[ConversationStatus] = None,
        mode: Optional[ChatGPTMode] = None,
        search: Optional[str] = None,
        created_after: Optional[datetime] = None,
        created_before: Optional[datetime] = None
    ) -> ConversationListResponse:
        """
        Get conversation list with filtering and pagination
        
        Args:
            page: Page number (starting from 1)
            page_size: Number of conversations per page (1-100)
            status: Filter by conversation status
            mode: Filter by conversation mode
            search: Search in conversation titles and prompts
            created_after: Filter conversations created after this time
            created_before: Filter conversations created before this time
            
        Returns:
            ConversationListResponse: List of conversations with pagination info
        """
        params = ConversationQueryParams(
            page=page,
            page_size=page_size,
            status=status,
            mode=mode,
            search=search,
            created_after=created_after,
            created_before=created_before
        )
        
        return await self._make_request(
            "GET",
            "/conversations",
            params=params.model_dump(exclude_none=True),
            response_model=ConversationListResponse
        )
    
    async def get_conversation(
        self,
        conversation_id: str,
        use_cache: bool = True
    ) -> ConversationResponse:
        """
        Get conversation details including messages
        
        Args:
            conversation_id: ID of the conversation
            use_cache: Whether to use cached data if available
            
        Returns:
            ConversationResponse: Conversation details and messages
        """
        params = {"use_cache": use_cache}
        
        return await self._make_request(
            "GET",
            f"/conversations/{conversation_id}",
            params=params,
            response_model=ConversationResponse
        )
    
    async def send_message(
        self,
        conversation_id: str,
        message: str
    ) -> MessageResponse:
        """
        Send a message to a conversation
        
        Args:
            conversation_id: ID of the target conversation
            message: Message content to send
            
        Returns:
            MessageResponse: Message sending result
        """
        request_data = MessageRequest(message=message)
        
        return await self._make_request(
            "POST",
            f"/conversations/{conversation_id}/messages",
            json_data=request_data.model_dump(),
            response_model=MessageResponse
        )
