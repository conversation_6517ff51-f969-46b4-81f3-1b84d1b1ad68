"""
Exception classes for ChatGPT Controller API Client
"""

from typing import Optional


class ChatGPTClientError(Exception):
    """Base exception for ChatGPT Controller client errors"""
    
    def __init__(self, message: str, status_code: Optional[int] = None):
        super().__init__(message)
        self.message = message
        self.status_code = status_code


class APIError(ChatGPTClientError):
    """Exception for API-related errors"""
    pass


class ValidationError(ChatGPTClientError):
    """Exception for validation errors"""
    pass


class ConnectionError(ChatGPTClientError):
    """Exception for connection-related errors"""
    pass


class AuthenticationError(ChatGPTClientError):
    """Exception for authentication errors"""
    pass


class NotFoundError(APIError):
    """Exception for 404 Not Found errors"""
    
    def __init__(self, message: str):
        super().__init__(message, status_code=404)


class ServerError(APIError):
    """Exception for 5xx server errors"""
    
    def __init__(self, message: str, status_code: int = 500):
        super().__init__(message, status_code=status_code)
