"""
ChatGPT Controller API Client

A Python client library for the ChatGPT Controller API with CLI, GUI, and TUI interfaces.
"""

__version__ = "0.1.0"
__author__ = "ChatGPT Controller Team"

from .client import ChatGPTClient
from .config import config_manager, ClientConfig
from .models import *
from .exceptions import *
from .i18n import Language, set_language, detect_language

__all__ = [
    "ChatGPTClient",
    "config_manager",
    "ClientConfig",
    "Language",
    "set_language",
    "detect_language",
]
